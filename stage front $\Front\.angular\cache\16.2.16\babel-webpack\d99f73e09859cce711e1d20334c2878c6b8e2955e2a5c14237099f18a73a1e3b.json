{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UserService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = '/api/utilisateurs';\n  }\n  // Le backend retourne des DTOs, pas des entités directement\n  getUtilisateurs() {\n    return this.http.get(this.API_URL);\n  }\n  getUtilisateur(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  // Le backend utilise des DTOs spécifiques\n  createUtilisateur(userData) {\n    return this.http.post(this.API_URL, userData);\n  }\n  updateUtilisateur(id, userData) {\n    return this.http.put(`${this.API_URL}/${id}`, userData);\n  }\n  deleteUtilisateur(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  static {\n    this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["UserService", "constructor", "http", "API_URL", "getUtilisateurs", "get", "getUtilisateur", "id", "createUtilisateur", "userData", "post", "updateUtilisateur", "put", "deleteUtilisateur", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private readonly API_URL = '/api/utilisateurs';\n\n  constructor(private http: HttpClient) {}\n\n  // Le backend retourne des DTOs, pas des entités directement\n  getUtilisateurs(): Observable<any[]> {\n    return this.http.get<any[]>(this.API_URL);\n  }\n\n  getUtilisateur(id: string): Observable<any> {\n    return this.http.get<any>(`${this.API_URL}/${id}`);\n  }\n\n  // Le backend utilise des DTOs spécifiques\n  createUtilisateur(userData: any): Observable<any> {\n    return this.http.post<any>(this.API_URL, userData);\n  }\n\n  updateUtilisateur(id: string, userData: any): Observable<void> {\n    return this.http.put<void>(`${this.API_URL}/${id}`, userData);\n  }\n\n  deleteUtilisateur(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Ces endpoints n'existent pas dans le backend actuel\n  // Seuls les CRUD de base sont disponibles\n}\n"], "mappings": ";;AAOA,OAAM,MAAOA,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,mBAAmB;EAEP;EAEvC;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAQ,IAAI,CAACF,OAAO,CAAC;EAC3C;EAEAG,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EACpD;EAEA;EACAC,iBAAiBA,CAACC,QAAa;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAM,IAAI,CAACP,OAAO,EAAEM,QAAQ,CAAC;EACpD;EAEAE,iBAAiBA,CAACJ,EAAU,EAAEE,QAAa;IACzC,OAAO,IAAI,CAACP,IAAI,CAACU,GAAG,CAAO,GAAG,IAAI,CAACT,OAAO,IAAII,EAAE,EAAE,EAAEE,QAAQ,CAAC;EAC/D;EAEAI,iBAAiBA,CAACN,EAAU;IAC1B,OAAO,IAAI,CAACL,IAAI,CAACY,MAAM,CAAO,GAAG,IAAI,CAACX,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;;;uBAzBWP,WAAW,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXlB,WAAW;MAAAmB,OAAA,EAAXnB,WAAW,CAAAoB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}