{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/card\";\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    // Rediriger vers le dashboard approprié selon le rôle\n    if (this.authService.isAdmin()) {\n      this.router.navigate(['/admin-dashboard']);\n    } else {\n      // Rester sur le dashboard utilisateur\n    }\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/login']);\n      },\n      error: () => {\n        // Forcer la déconnexion même en cas d'erreur\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 32,\n      vars: 0,\n      consts: [[1, \"dashboard-container\"], [1, \"welcome-section\"], [1, \"actions-section\"], [1, \"action-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/user-documents\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/retenue-a-la-source\"], [1, \"logout-section\"], [\"mat-button\", \"\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Dashboard Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"p\");\n          i0.ɵɵtext(5, \"Bienvenue dans votre espace personnel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"mat-card\", 3)(8, \"mat-card-header\")(9, \"mat-card-title\");\n          i0.ɵɵtext(10, \"Mes Documents\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"p\");\n          i0.ɵɵtext(13, \"G\\u00E9rez vos demandes de documents\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"mat-card-actions\")(15, \"button\", 4);\n          i0.ɵɵtext(16, \" Acc\\u00E9der \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"mat-card\", 3)(18, \"mat-card-header\")(19, \"mat-card-title\");\n          i0.ɵɵtext(20, \"Retenues \\u00E0 la Source\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"mat-card-content\")(22, \"p\");\n          i0.ɵɵtext(23, \"Consultez vos retenues \\u00E0 la source\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"mat-card-actions\")(25, \"button\", 5);\n          i0.ɵɵtext(26, \" Acc\\u00E9der \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 6)(28, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_28_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelementStart(29, \"mat-icon\");\n          i0.ɵɵtext(30, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" Se d\\u00E9connecter \");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i2.RouterLink, i3.MatButton, i4.MatIcon, i5.MatCard, i5.MatCardActions, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  text-align: center;\\n}\\n\\n.actions-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n  margin-bottom: 32px;\\n}\\n\\n.action-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.logout-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 32px;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #333;\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZGFzaGJvYXJkL2Rhc2hib2FyZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBYTtFQUNiLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiwyREFBMkQ7RUFDM0QsU0FBUztFQUNULG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLG1CQUFtQjtBQUNyQiIsInNvdXJjZXNDb250ZW50IjpbIi5kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMjRweDtcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG4ud2VsY29tZS1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uYWN0aW9ucy1zZWN0aW9uIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gIGdhcDogMjRweDtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbn1cblxuLmFjdGlvbi1jYXJkIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4ubG9nb3V0LXNlY3Rpb24ge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi10b3A6IDMycHg7XG59XG5cbmgxIHtcbiAgY29sb3I6ICMzMzM7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "authService", "router", "ngOnInit", "isAdmin", "navigate", "logout", "subscribe", "next", "error", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardComponent_Template_button_click_28_listener"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../services/auth.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Rediriger vers le dashboard approprié selon le rôle\n    if (this.authService.isAdmin()) {\n      this.router.navigate(['/admin-dashboard']);\n    } else {\n      // Rester sur le dashboard utilisateur\n    }\n  }\n\n  logout(): void {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/login']);\n      },\n      error: () => {\n        // Forcer la déconnexion même en cas d'erreur\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n}\n", "<div class=\"dashboard-container\">\n  <h1>Dashboard Utilisateur</h1>\n  \n  <div class=\"welcome-section\">\n    <p>Bienvenue dans votre espace personnel</p>\n  </div>\n\n  <div class=\"actions-section\">\n    <mat-card class=\"action-card\">\n      <mat-card-header>\n        <mat-card-title>Mes Documents</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>G<PERSON>rez vos demandes de documents</p>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-raised-button color=\"primary\" routerLink=\"/user-documents\">\n          Accéder\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <mat-card class=\"action-card\">\n      <mat-card-header>\n        <mat-card-title>Retenues à la Source</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Consultez vos retenues à la source</p>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-raised-button color=\"primary\" routerLink=\"/retenue-a-la-source\">\n          Accéder\n        </button>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n\n  <div class=\"logout-section\">\n    <button mat-button (click)=\"logout()\">\n      <mat-icon>logout</mat-icon>\n      Se déconnecter\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;;AASA,OAAM,MAAOA,kBAAkB;EAE7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACF,WAAW,CAACG,OAAO,EAAE,EAAE;MAC9B,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;KAC3C,MAAM;MACL;IAAA;EAEJ;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE,CAACC,SAAS,CAAC;MAClCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACN,MAAM,CAACG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDI,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACP,MAAM,CAACG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;;;uBA1BWN,kBAAkB,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBhB,kBAAkB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BZ,EAAA,CAAAc,cAAA,aAAiC;UAC3Bd,EAAA,CAAAe,MAAA,4BAAqB;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAE9BhB,EAAA,CAAAc,cAAA,aAA6B;UACxBd,EAAA,CAAAe,MAAA,4CAAqC;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAG9ChB,EAAA,CAAAc,cAAA,aAA6B;UAGPd,EAAA,CAAAe,MAAA,qBAAa;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UAEhDhB,EAAA,CAAAc,cAAA,wBAAkB;UACbd,EAAA,CAAAe,MAAA,4CAA+B;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAExChB,EAAA,CAAAc,cAAA,wBAAkB;UAEdd,EAAA,CAAAe,MAAA,sBACF;UAAAf,EAAA,CAAAgB,YAAA,EAAS;UAIbhB,EAAA,CAAAc,cAAA,mBAA8B;UAEVd,EAAA,CAAAe,MAAA,iCAAoB;UAAAf,EAAA,CAAAgB,YAAA,EAAiB;UAEvDhB,EAAA,CAAAc,cAAA,wBAAkB;UACbd,EAAA,CAAAe,MAAA,+CAAkC;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAE3ChB,EAAA,CAAAc,cAAA,wBAAkB;UAEdd,EAAA,CAAAe,MAAA,sBACF;UAAAf,EAAA,CAAAgB,YAAA,EAAS;UAKfhB,EAAA,CAAAc,cAAA,cAA4B;UACPd,EAAA,CAAAiB,UAAA,mBAAAC,qDAAA;YAAA,OAASL,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UACnCI,EAAA,CAAAc,cAAA,gBAAU;UAAAd,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAW;UAC3BhB,EAAA,CAAAe,MAAA,6BACF;UAAAf,EAAA,CAAAgB,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}