{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RetenueSourceService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = '/api/RetenueSource';\n  }\n  getRetenuesSource() {\n    return this.http.get(this.API_URL);\n  }\n  getRetenueSource(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  uploadRetenueSource(file, dateScan) {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    if (dateScan) {\n      formData.append('dateScan', dateScan.toISOString());\n    }\n    return this.http.post(this.API_URL, formData);\n  }\n  deleteRetenueSource(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  downloadFile(id) {\n    return this.http.get(`${this.API_URL}/${id}/download`, {\n      responseType: 'blob'\n    });\n  }\n  scanDocument(file) {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    return this.http.post(`${this.API_URL}/scan`, formData);\n  }\n  getRetenuesByDateRange(dateDebut, dateFin) {\n    const params = new HttpParams().set('dateDebut', dateDebut.toISOString()).set('dateFin', dateFin.toISOString());\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  getRetenuesByUtilisateur(utilisateurId) {\n    const params = new HttpParams().set('utilisateurId', utilisateurId);\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function RetenueSourceService_Factory(t) {\n      return new (t || RetenueSourceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RetenueSourceService,\n      factory: RetenueSourceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "RetenueSourceService", "constructor", "http", "API_URL", "getRetenuesSource", "get", "getRetenueSource", "id", "uploadRetenueSource", "file", "dateScan", "formData", "FormData", "append", "toISOString", "post", "deleteRetenueSource", "delete", "downloadFile", "responseType", "scanDocument", "getRetenuesByDateRange", "dateDebut", "dateFin", "params", "set", "getRetenuesByUtilisateur", "utilisateurId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\retenue-source.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { RetenueSource } from '../app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RetenueSourceService {\n  private readonly API_URL = '/api/RetenueSource';\n\n  constructor(private http: HttpClient) {}\n\n  getRetenuesSource(): Observable<RetenueSource[]> {\n    return this.http.get<RetenueSource[]>(this.API_URL);\n  }\n\n  getRetenueSource(id: string): Observable<RetenueSource> {\n    return this.http.get<RetenueSource>(`${this.API_URL}/${id}`);\n  }\n\n  uploadRetenueSource(file: File, dateScan?: Date): Observable<RetenueSource> {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    \n    if (dateScan) {\n      formData.append('dateScan', dateScan.toISOString());\n    }\n    \n    return this.http.post<RetenueSource>(this.API_URL, formData);\n  }\n\n  deleteRetenueSource(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  downloadFile(id: string): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/${id}/download`, { \n      responseType: 'blob' \n    });\n  }\n\n  scanDocument(file: File): Observable<any> {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    \n    return this.http.post(`${this.API_URL}/scan`, formData);\n  }\n\n  getRetenuesByDateRange(dateDebut: Date, dateFin: Date): Observable<RetenueSource[]> {\n    const params = new HttpParams()\n      .set('dateDebut', dateDebut.toISOString())\n      .set('dateFin', dateFin.toISOString());\n    \n    return this.http.get<RetenueSource[]>(this.API_URL, { params });\n  }\n\n  getRetenuesByUtilisateur(utilisateurId: string): Observable<RetenueSource[]> {\n    const params = new HttpParams().set('utilisateurId', utilisateurId);\n    return this.http.get<RetenueSource[]>(this.API_URL, { params });\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAO7D,OAAM,MAAOC,oBAAoB;EAG/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,oBAAoB;EAER;EAEvCC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,CAAC;EACrD;EAEAG,gBAAgBA,CAACC,EAAU;IACzB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAgB,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EAC9D;EAEAC,mBAAmBA,CAACC,IAAU,EAAEC,QAAe;IAC7C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,IAAI,CAAC;IAEhC,IAAIC,QAAQ,EAAE;MACZC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAACI,WAAW,EAAE,CAAC;;IAGrD,OAAO,IAAI,CAACZ,IAAI,CAACa,IAAI,CAAgB,IAAI,CAACZ,OAAO,EAAEQ,QAAQ,CAAC;EAC9D;EAEAK,mBAAmBA,CAACT,EAAU;IAC5B,OAAO,IAAI,CAACL,IAAI,CAACe,MAAM,CAAO,GAAG,IAAI,CAACd,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;EAEAW,YAAYA,CAACX,EAAU;IACrB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,WAAW,EAAE;MACrDY,YAAY,EAAE;KACf,CAAC;EACJ;EAEAC,YAAYA,CAACX,IAAU;IACrB,MAAME,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,IAAI,CAAC;IAEhC,OAAO,IAAI,CAACP,IAAI,CAACa,IAAI,CAAC,GAAG,IAAI,CAACZ,OAAO,OAAO,EAAEQ,QAAQ,CAAC;EACzD;EAEAU,sBAAsBA,CAACC,SAAe,EAAEC,OAAa;IACnD,MAAMC,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAC5B0B,GAAG,CAAC,WAAW,EAAEH,SAAS,CAACR,WAAW,EAAE,CAAC,CACzCW,GAAG,CAAC,SAAS,EAAEF,OAAO,CAACT,WAAW,EAAE,CAAC;IAExC,OAAO,IAAI,CAACZ,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,EAAE;MAAEqB;IAAM,CAAE,CAAC;EACjE;EAEAE,wBAAwBA,CAACC,aAAqB;IAC5C,MAAMH,MAAM,GAAG,IAAIzB,UAAU,EAAE,CAAC0B,GAAG,CAAC,eAAe,EAAEE,aAAa,CAAC;IACnE,OAAO,IAAI,CAACzB,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,EAAE;MAAEqB;IAAM,CAAE,CAAC;EACjE;;;uBApDWxB,oBAAoB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApB/B,oBAAoB;MAAAgC,OAAA,EAApBhC,oBAAoB,CAAAiC,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}