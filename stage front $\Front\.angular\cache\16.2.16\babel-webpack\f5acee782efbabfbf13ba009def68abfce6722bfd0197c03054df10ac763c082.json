{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FactureAchatService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5251/api/factures-achat';\n  }\n  getFacturesAchat() {\n    return this.http.get(this.API_URL);\n  }\n  getFactureAchat(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  createFactureAchat(factureData) {\n    const formData = new FormData();\n    // Ajouter les données de la facture\n    formData.append('numero', factureData.numero);\n    formData.append('date', factureData.date.toISOString());\n    formData.append('montant', factureData.montant.toString());\n    formData.append('fournisseurId', factureData.fournisseurId);\n    if (factureData.dateEcheance) {\n      formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n    }\n    if (factureData.notesInternes) {\n      formData.append('notesInternes', factureData.notesInternes);\n    }\n    if (factureData.statut !== undefined) {\n      formData.append('statut', factureData.statut.toString());\n    }\n    // Ajouter le fichier si présent\n    if (factureData.fichier) {\n      formData.append('fichier', factureData.fichier);\n    }\n    return this.http.post(this.API_URL, formData);\n  }\n  updateFactureAchat(id, factureData) {\n    const formData = new FormData();\n    formData.append('numero', factureData.numero);\n    formData.append('date', factureData.date.toISOString());\n    formData.append('montant', factureData.montant.toString());\n    formData.append('fournisseurId', factureData.fournisseurId);\n    formData.append('statut', factureData.statut.toString());\n    if (factureData.dateEcheance) {\n      formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n    }\n    if (factureData.notesInternes) {\n      formData.append('notesInternes', factureData.notesInternes);\n    }\n    if (factureData.fichier) {\n      formData.append('fichier', factureData.fichier);\n    }\n    return this.http.put(`${this.API_URL}/${id}`, formData);\n  }\n  deleteFactureAchat(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  downloadFile(id) {\n    return this.http.get(`${this.API_URL}/${id}/download`, {\n      responseType: 'blob'\n    });\n  }\n  uploadFile(id, file) {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    return this.http.post(`${this.API_URL}/${id}/upload`, formData);\n  }\n  getFacturesByFournisseur(fournisseurId) {\n    const params = new HttpParams().set('fournisseurId', fournisseurId);\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  getFacturesByStatut(statut) {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  searchFactures(searchTerm) {\n    const params = new HttpParams().set('search', searchTerm);\n    return this.http.get(`${this.API_URL}/search`, {\n      params\n    });\n  }\n  // Méthodes pour l'upload de fichiers\n  createFactureAchatWithFile(formData) {\n    return this.http.post(`${this.API_URL}/with-file`, formData);\n  }\n  updateFactureAchatWithFile(id, formData) {\n    return this.http.put(`${this.API_URL}/${id}/with-file`, formData);\n  }\n  deleteFile(factureId) {\n    return this.http.delete(`${this.API_URL}/${factureId}/file`);\n  }\n  static {\n    this.ɵfac = function FactureAchatService_Factory(t) {\n      return new (t || FactureAchatService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FactureAchatService,\n      factory: FactureAchatService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "FactureAchatService", "constructor", "http", "API_URL", "getFacturesAchat", "get", "getFactureAchat", "id", "createFactureAchat", "factureData", "formData", "FormData", "append", "numero", "date", "toISOString", "montant", "toString", "fournisseurId", "dateEcheance", "notesInternes", "statut", "undefined", "<PERSON><PERSON><PERSON>", "post", "updateFactureAchat", "put", "deleteFactureAchat", "delete", "downloadFile", "responseType", "uploadFile", "file", "getFacturesByFournisseur", "params", "set", "getFacturesByStatut", "searchFactures", "searchTerm", "createFactureAchatWithFile", "updateFactureAchatWithFile", "deleteFile", "factureId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\facture-achat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { FactureAchat } from 'src/app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FactureAchatService {\n  private readonly API_URL = 'http://localhost:5251/api/factures-achat';\n\n  constructor(private http: HttpClient) {}\n\n  getFacturesAchat(): Observable<FactureAchat[]> {\n    return this.http.get<FactureAchat[]>(this.API_URL);\n  }\n\n  getFactureAchat(id: string): Observable<FactureAchat> {\n    return this.http.get<FactureAchat>(`${this.API_URL}/${id}`);\n  }\n\n  createFactureAchat(factureData: any): Observable<FactureAchat> {\n    const formData = new FormData();\n    \n    // Ajouter les données de la facture\n    formData.append('numero', factureData.numero);\n    formData.append('date', factureData.date.toISOString());\n    formData.append('montant', factureData.montant.toString());\n    formData.append('fournisseurId', factureData.fournisseurId);\n    \n    if (factureData.dateEcheance) {\n      formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n    }\n    \n    if (factureData.notesInternes) {\n      formData.append('notesInternes', factureData.notesInternes);\n    }\n    \n    if (factureData.statut !== undefined) {\n      formData.append('statut', factureData.statut.toString());\n    }\n    \n    // Ajouter le fichier si présent\n    if (factureData.fichier) {\n      formData.append('fichier', factureData.fichier);\n    }\n    \n    return this.http.post<FactureAchat>(this.API_URL, formData);\n  }\n\n  updateFactureAchat(id: string, factureData: any): Observable<FactureAchat> {\n    const formData = new FormData();\n    \n    formData.append('numero', factureData.numero);\n    formData.append('date', factureData.date.toISOString());\n    formData.append('montant', factureData.montant.toString());\n    formData.append('fournisseurId', factureData.fournisseurId);\n    formData.append('statut', factureData.statut.toString());\n    \n    if (factureData.dateEcheance) {\n      formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n    }\n    \n    if (factureData.notesInternes) {\n      formData.append('notesInternes', factureData.notesInternes);\n    }\n    \n    if (factureData.fichier) {\n      formData.append('fichier', factureData.fichier);\n    }\n    \n    return this.http.put<FactureAchat>(`${this.API_URL}/${id}`, formData);\n  }\n\n  deleteFactureAchat(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  downloadFile(id: string): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/${id}/download`, { \n      responseType: 'blob' \n    });\n  }\n\n  uploadFile(id: string, file: File): Observable<any> {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    return this.http.post(`${this.API_URL}/${id}/upload`, formData);\n  }\n\n  getFacturesByFournisseur(fournisseurId: string): Observable<FactureAchat[]> {\n    const params = new HttpParams().set('fournisseurId', fournisseurId);\n    return this.http.get<FactureAchat[]>(this.API_URL, { params });\n  }\n\n  getFacturesByStatut(statut: number): Observable<FactureAchat[]> {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get<FactureAchat[]>(this.API_URL, { params });\n  }\n\n  searchFactures(searchTerm: string): Observable<FactureAchat[]> {\n    const params = new HttpParams().set('search', searchTerm);\n    return this.http.get<FactureAchat[]>(`${this.API_URL}/search`, { params });\n  }\n\n  // Méthodes pour l'upload de fichiers\n  createFactureAchatWithFile(formData: FormData): Observable<FactureAchat> {\n    return this.http.post<FactureAchat>(`${this.API_URL}/with-file`, formData);\n  }\n\n  updateFactureAchatWithFile(id: string, formData: FormData): Observable<FactureAchat> {\n    return this.http.put<FactureAchat>(`${this.API_URL}/${id}/with-file`, formData);\n  }\n\n\n\n  deleteFile(factureId: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${factureId}/file`);\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAO7D,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,0CAA0C;EAE9B;EAEvCC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,CAAC;EACpD;EAEAG,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAe,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EAC7D;EAEAC,kBAAkBA,CAACC,WAAgB;IACjC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,WAAW,CAACI,MAAM,CAAC;IAC7CH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,WAAW,CAACK,IAAI,CAACC,WAAW,EAAE,CAAC;IACvDL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,WAAW,CAACO,OAAO,CAACC,QAAQ,EAAE,CAAC;IAC1DP,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,WAAW,CAACS,aAAa,CAAC;IAE3D,IAAIT,WAAW,CAACU,YAAY,EAAE;MAC5BT,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEH,WAAW,CAACU,YAAY,CAACJ,WAAW,EAAE,CAAC;;IAGzE,IAAIN,WAAW,CAACW,aAAa,EAAE;MAC7BV,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,WAAW,CAACW,aAAa,CAAC;;IAG7D,IAAIX,WAAW,CAACY,MAAM,KAAKC,SAAS,EAAE;MACpCZ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,WAAW,CAACY,MAAM,CAACJ,QAAQ,EAAE,CAAC;;IAG1D;IACA,IAAIR,WAAW,CAACc,OAAO,EAAE;MACvBb,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,WAAW,CAACc,OAAO,CAAC;;IAGjD,OAAO,IAAI,CAACrB,IAAI,CAACsB,IAAI,CAAe,IAAI,CAACrB,OAAO,EAAEO,QAAQ,CAAC;EAC7D;EAEAe,kBAAkBA,CAAClB,EAAU,EAAEE,WAAgB;IAC7C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,WAAW,CAACI,MAAM,CAAC;IAC7CH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,WAAW,CAACK,IAAI,CAACC,WAAW,EAAE,CAAC;IACvDL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,WAAW,CAACO,OAAO,CAACC,QAAQ,EAAE,CAAC;IAC1DP,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,WAAW,CAACS,aAAa,CAAC;IAC3DR,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,WAAW,CAACY,MAAM,CAACJ,QAAQ,EAAE,CAAC;IAExD,IAAIR,WAAW,CAACU,YAAY,EAAE;MAC5BT,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEH,WAAW,CAACU,YAAY,CAACJ,WAAW,EAAE,CAAC;;IAGzE,IAAIN,WAAW,CAACW,aAAa,EAAE;MAC7BV,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,WAAW,CAACW,aAAa,CAAC;;IAG7D,IAAIX,WAAW,CAACc,OAAO,EAAE;MACvBb,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,WAAW,CAACc,OAAO,CAAC;;IAGjD,OAAO,IAAI,CAACrB,IAAI,CAACwB,GAAG,CAAe,GAAG,IAAI,CAACvB,OAAO,IAAII,EAAE,EAAE,EAAEG,QAAQ,CAAC;EACvE;EAEAiB,kBAAkBA,CAACpB,EAAU;IAC3B,OAAO,IAAI,CAACL,IAAI,CAAC0B,MAAM,CAAO,GAAG,IAAI,CAACzB,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;EAEAsB,YAAYA,CAACtB,EAAU;IACrB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,WAAW,EAAE;MACrDuB,YAAY,EAAE;KACf,CAAC;EACJ;EAEAC,UAAUA,CAACxB,EAAU,EAAEyB,IAAU;IAC/B,MAAMtB,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEoB,IAAI,CAAC;IAChC,OAAO,IAAI,CAAC9B,IAAI,CAACsB,IAAI,CAAC,GAAG,IAAI,CAACrB,OAAO,IAAII,EAAE,SAAS,EAAEG,QAAQ,CAAC;EACjE;EAEAuB,wBAAwBA,CAACf,aAAqB;IAC5C,MAAMgB,MAAM,GAAG,IAAInC,UAAU,EAAE,CAACoC,GAAG,CAAC,eAAe,EAAEjB,aAAa,CAAC;IACnE,OAAO,IAAI,CAAChB,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,EAAE;MAAE+B;IAAM,CAAE,CAAC;EAChE;EAEAE,mBAAmBA,CAACf,MAAc;IAChC,MAAMa,MAAM,GAAG,IAAInC,UAAU,EAAE,CAACoC,GAAG,CAAC,QAAQ,EAAEd,MAAM,CAACJ,QAAQ,EAAE,CAAC;IAChE,OAAO,IAAI,CAACf,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,EAAE;MAAE+B;IAAM,CAAE,CAAC;EAChE;EAEAG,cAAcA,CAACC,UAAkB;IAC/B,MAAMJ,MAAM,GAAG,IAAInC,UAAU,EAAE,CAACoC,GAAG,CAAC,QAAQ,EAAEG,UAAU,CAAC;IACzD,OAAO,IAAI,CAACpC,IAAI,CAACG,GAAG,CAAiB,GAAG,IAAI,CAACF,OAAO,SAAS,EAAE;MAAE+B;IAAM,CAAE,CAAC;EAC5E;EAEA;EACAK,0BAA0BA,CAAC7B,QAAkB;IAC3C,OAAO,IAAI,CAACR,IAAI,CAACsB,IAAI,CAAe,GAAG,IAAI,CAACrB,OAAO,YAAY,EAAEO,QAAQ,CAAC;EAC5E;EAEA8B,0BAA0BA,CAACjC,EAAU,EAAEG,QAAkB;IACvD,OAAO,IAAI,CAACR,IAAI,CAACwB,GAAG,CAAe,GAAG,IAAI,CAACvB,OAAO,IAAII,EAAE,YAAY,EAAEG,QAAQ,CAAC;EACjF;EAIA+B,UAAUA,CAACC,SAAiB;IAC1B,OAAO,IAAI,CAACxC,IAAI,CAAC0B,MAAM,CAAO,GAAG,IAAI,CAACzB,OAAO,IAAIuC,SAAS,OAAO,CAAC;EACpE;;;uBA9GW1C,mBAAmB,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB9C,mBAAmB;MAAA+C,OAAA,EAAnB/C,mBAAmB,CAAAgD,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}