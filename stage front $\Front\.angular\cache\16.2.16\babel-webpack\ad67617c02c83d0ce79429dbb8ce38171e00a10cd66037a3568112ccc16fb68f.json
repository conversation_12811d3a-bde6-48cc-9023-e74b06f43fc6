{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/card\";\nfunction RegisterComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom d'utilisateur est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom d'utilisateur doit contenir au moins 3 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Format d'email invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le mot de passe doit contenir au moins 6 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"La confirmation est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Les mots de passe ne correspondent pas\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom de la soci\\u00E9t\\u00E9 est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"L'adresse est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le matricule fiscal est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le matricule doit contenir au moins 8 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Format d'email invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.error);\n  }\n}\nfunction RegisterComponent_mat_icon_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9er le compte\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9ation en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n  }\n  ngOnInit() {\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n    this.registerForm = this.formBuilder.group({\n      // Informations utilisateur\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      userName: ['', [Validators.required, Validators.minLength(3)]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]],\n      confirmMotDePasse: ['', [Validators.required]],\n      // Informations société\n      nomSociete: ['', [Validators.required, Validators.minLength(2)]],\n      adresseSociete: ['', [Validators.required]],\n      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],\n      emailSociete: ['', [Validators.email]],\n      telephoneSociete: ['']\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  // Validateur personnalisé pour vérifier que les mots de passe correspondent\n  passwordMatchValidator(form) {\n    const password = form.get('motDePasse');\n    const confirmPassword = form.get('confirmMotDePasse');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  onSubmit() {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    const formValue = this.registerForm.value;\n    // Préparer les données pour l'inscription\n    // Le premier utilisateur sera automatiquement Admin\n    const registrationData = {\n      // Données utilisateur\n      nom: formValue.nom,\n      email: formValue.email,\n      userName: formValue.userName,\n      motDePasse: formValue.motDePasse,\n      // Données société (création automatique)\n      societe: {\n        nom: formValue.nomSociete,\n        adresse: formValue.adresseSociete,\n        matriculeFiscale: formValue.matriculeFiscale,\n        email: formValue.emailSociete,\n        telephone: formValue.telephoneSociete\n      }\n    };\n    this.authService.register(registrationData).subscribe({\n      next: () => {\n        this.loading = false;\n        // Redirection automatique après inscription réussie\n        this.router.navigate(['/admin-dashboard']);\n      },\n      error: error => {\n        this.loading = false;\n        this.error = error.error?.message || 'Erreur lors de l\\'inscription. Veuillez réessayer.';\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() {\n    return this.registerForm.get('nom');\n  }\n  get email() {\n    return this.registerForm.get('email');\n  }\n  get userName() {\n    return this.registerForm.get('userName');\n  }\n  get motDePasse() {\n    return this.registerForm.get('motDePasse');\n  }\n  get confirmMotDePasse() {\n    return this.registerForm.get('confirmMotDePasse');\n  }\n  get nomSociete() {\n    return this.registerForm.get('nomSociete');\n  }\n  get adresseSociete() {\n    return this.registerForm.get('adresseSociete');\n  }\n  get matriculeFiscale() {\n    return this.registerForm.get('matriculeFiscale');\n  }\n  get emailSociete() {\n    return this.registerForm.get('emailSociete');\n  }\n  get telephoneSociete() {\n    return this.registerForm.get('telephoneSociete');\n  }\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  toggleConfirmPasswordVisibility() {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 108,\n      vars: 26,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"section-title\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"nom\", \"placeholder\", \"Votre nom complet\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"userName\", \"placeholder\", \"Nom d'utilisateur\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"motDePasse\", \"placeholder\", \"Mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmMotDePasse\", \"placeholder\", \"Confirmer le mot de passe\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"nomSociete\", \"placeholder\", \"Nom de votre soci\\u00E9t\\u00E9\"], [\"matInput\", \"\", \"formControlName\", \"adresseSociete\", \"placeholder\", \"Adresse compl\\u00E8te de la soci\\u00E9t\\u00E9\", \"rows\", \"3\"], [\"matInput\", \"\", \"formControlName\", \"matriculeFiscale\", \"placeholder\", \"Matricule fiscal unique\"], [\"matInput\", \"\", \"formControlName\", \"telephoneSociete\", \"placeholder\", \"T\\u00E9l\\u00E9phone de la soci\\u00E9t\\u00E9\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"emailSociete\", \"placeholder\", \"<EMAIL>\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [\"align\", \"center\"], [\"routerLink\", \"/login\", 1, \"login-link\"], [1, \"error-message\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Inscription\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"Cr\\u00E9ez votre compte et votre soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(9, \"h3\", 3)(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" Informations Utilisateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"mat-form-field\", 5)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Nom complet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 6);\n          i0.ɵɵelementStart(18, \"mat-icon\", 7);\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, RegisterComponent_mat_error_20_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(21, RegisterComponent_mat_error_21_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 5)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Nom d'utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 9);\n          i0.ɵɵelementStart(26, \"mat-icon\", 7);\n          i0.ɵɵtext(27, \"account_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, RegisterComponent_mat_error_28_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(29, RegisterComponent_mat_error_29_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 10)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 11);\n          i0.ɵɵelementStart(34, \"mat-icon\", 7);\n          i0.ɵɵtext(35, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, RegisterComponent_mat_error_36_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(37, RegisterComponent_mat_error_37_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 4)(39, \"mat-form-field\", 5)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 12);\n          i0.ɵɵelementStart(43, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_43_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementStart(44, \"mat-icon\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, RegisterComponent_mat_error_46_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(47, RegisterComponent_mat_error_47_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"mat-form-field\", 5)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Confirmer le mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 14);\n          i0.ɵɵelementStart(52, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_52_listener() {\n            return ctx.toggleConfirmPasswordVisibility();\n          });\n          i0.ɵɵelementStart(53, \"mat-icon\");\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(55, RegisterComponent_mat_error_55_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(56, RegisterComponent_mat_error_56_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"h3\", 3)(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Informations Soci\\u00E9t\\u00E9 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 10)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Nom de la soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 15);\n          i0.ɵɵelementStart(65, \"mat-icon\", 7);\n          i0.ɵɵtext(66, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(67, RegisterComponent_mat_error_67_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(68, RegisterComponent_mat_error_68_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"mat-form-field\", 10)(70, \"mat-label\");\n          i0.ɵɵtext(71, \"Adresse de la soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"textarea\", 16);\n          i0.ɵɵelementStart(73, \"mat-icon\", 7);\n          i0.ɵɵtext(74, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(75, RegisterComponent_mat_error_75_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 4)(77, \"mat-form-field\", 5)(78, \"mat-label\");\n          i0.ɵɵtext(79, \"Matricule fiscal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 17);\n          i0.ɵɵelementStart(81, \"mat-icon\", 7);\n          i0.ɵɵtext(82, \"receipt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(83, RegisterComponent_mat_error_83_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵtemplate(84, RegisterComponent_mat_error_84_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 5)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"T\\u00E9l\\u00E9phone (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(88, \"input\", 18);\n          i0.ɵɵelementStart(89, \"mat-icon\", 7);\n          i0.ɵɵtext(90, \"phone\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(91, \"mat-form-field\", 10)(92, \"mat-label\");\n          i0.ɵɵtext(93, \"Email soci\\u00E9t\\u00E9 (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(94, \"input\", 19);\n          i0.ɵɵelementStart(95, \"mat-icon\", 7);\n          i0.ɵɵtext(96, \"business_center\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(97, RegisterComponent_mat_error_97_Template, 2, 0, \"mat-error\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(98, RegisterComponent_div_98_Template, 5, 1, \"div\", 20);\n          i0.ɵɵelementStart(99, \"button\", 21);\n          i0.ɵɵtemplate(100, RegisterComponent_mat_icon_100_Template, 2, 0, \"mat-icon\", 8);\n          i0.ɵɵtemplate(101, RegisterComponent_span_101_Template, 2, 0, \"span\", 8);\n          i0.ɵɵtemplate(102, RegisterComponent_span_102_Template, 2, 0, \"span\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(103, \"mat-card-actions\", 22)(104, \"p\");\n          i0.ɵɵtext(105, \"D\\u00E9j\\u00E0 un compte ? \");\n          i0.ɵɵelementStart(106, \"a\", 23);\n          i0.ɵɵtext(107, \"Se connecter\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.nom == null ? null : ctx.nom.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.nom == null ? null : ctx.nom.hasError(\"minlength\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.userName == null ? null : ctx.userName.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userName == null ? null : ctx.userName.hasError(\"minlength\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"email\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.confirmMotDePasse == null ? null : ctx.confirmMotDePasse.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.confirmMotDePasse == null ? null : ctx.confirmMotDePasse.hasError(\"passwordMismatch\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.nomSociete == null ? null : ctx.nomSociete.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.nomSociete == null ? null : ctx.nomSociete.hasError(\"minlength\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.adresseSociete == null ? null : ctx.adresseSociete.hasError(\"required\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.hasError(\"minlength\"));\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.emailSociete == null ? null : ctx.emailSociete.hasError(\"email\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 600px;\\n  padding: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border-radius: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 24px 0 16px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n\\n.section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #667eea;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  margin-top: 24px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #f44336;\\n  margin-bottom: 16px;\\n  padding: 12px;\\n  background-color: #ffebee;\\n  border-radius: 8px;\\n  border-left: 4px solid #f44336;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.login-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\nmat-card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\nmat-card-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\nmat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 8px;\\n  font-size: 14px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n\\n  .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .register-card[_ngcontent-%COMP%] {\\n    margin: 10px;\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r16", "error", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "loading", "hidePassword", "hideConfirmPassword", "ngOnInit", "isAuthenticated", "navigate", "registerForm", "group", "nom", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "userName", "motDePasse", "confirmMotDePasse", "nomSociete", "adresseSociete", "matriculeFiscale", "emailSociete", "telephoneSociete", "validators", "passwordMatchValidator", "form", "password", "get", "confirmPassword", "value", "setErrors", "passwordMismatch", "onSubmit", "invalid", "markFormGroupTouched", "formValue", "registrationData", "societe", "adresse", "telephone", "register", "subscribe", "next", "message", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_8_listener", "ɵɵelement", "ɵɵtemplate", "RegisterComponent_mat_error_20_Template", "RegisterComponent_mat_error_21_Template", "RegisterComponent_mat_error_28_Template", "RegisterComponent_mat_error_29_Template", "RegisterComponent_mat_error_36_Template", "RegisterComponent_mat_error_37_Template", "RegisterComponent_Template_button_click_43_listener", "RegisterComponent_mat_error_46_Template", "RegisterComponent_mat_error_47_Template", "RegisterComponent_Template_button_click_52_listener", "RegisterComponent_mat_error_55_Template", "RegisterComponent_mat_error_56_Template", "RegisterComponent_mat_error_67_Template", "RegisterComponent_mat_error_68_Template", "RegisterComponent_mat_error_75_Template", "RegisterComponent_mat_error_83_Template", "RegisterComponent_mat_error_84_Template", "RegisterComponent_mat_error_97_Template", "RegisterComponent_div_98_Template", "RegisterComponent_mat_icon_100_Template", "RegisterComponent_span_101_Template", "RegisterComponent_span_102_Template", "ɵɵproperty", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\register\\register.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.css']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm!: FormGroup;\n  loading = false;\n  error = '';\n  hidePassword = true;\n  hideConfirmPassword = true;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n\n    this.registerForm = this.formBuilder.group({\n      // Informations utilisateur\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      userName: ['', [Validators.required, Validators.minLength(3)]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]],\n      confirmMotDePasse: ['', [Validators.required]],\n\n      // Informations société\n      nomSociete: ['', [Validators.required, Validators.minLength(2)]],\n      adresseSociete: ['', [Validators.required]],\n      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],\n      emailSociete: ['', [Validators.email]],\n      telephoneSociete: ['']\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  // Validateur personnalisé pour vérifier que les mots de passe correspondent\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('motDePasse');\n    const confirmPassword = form.get('confirmMotDePasse');\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n\n    return null;\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    const formValue = this.registerForm.value;\n\n    // Préparer les données pour l'inscription\n    // Le premier utilisateur sera automatiquement Admin\n    const registrationData = {\n      // Données utilisateur\n      nom: formValue.nom,\n      email: formValue.email,\n      userName: formValue.userName,\n      motDePasse: formValue.motDePasse,\n\n      // Données société (création automatique)\n      societe: {\n        nom: formValue.nomSociete,\n        adresse: formValue.adresseSociete,\n        matriculeFiscale: formValue.matriculeFiscale,\n        email: formValue.emailSociete,\n        telephone: formValue.telephoneSociete\n      }\n    };\n\n    this.authService.register(registrationData).subscribe({\n      next: () => {\n        this.loading = false;\n        // Redirection automatique après inscription réussie\n        this.router.navigate(['/admin-dashboard']);\n      },\n      error: (error) => {\n        this.loading = false;\n        this.error = error.error?.message || 'Erreur lors de l\\'inscription. Veuillez réessayer.';\n      }\n    });\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() { return this.registerForm.get('nom'); }\n  get email() { return this.registerForm.get('email'); }\n  get userName() { return this.registerForm.get('userName'); }\n  get motDePasse() { return this.registerForm.get('motDePasse'); }\n  get confirmMotDePasse() { return this.registerForm.get('confirmMotDePasse'); }\n  get nomSociete() { return this.registerForm.get('nomSociete'); }\n  get adresseSociete() { return this.registerForm.get('adresseSociete'); }\n  get matriculeFiscale() { return this.registerForm.get('matriculeFiscale'); }\n  get emailSociete() { return this.registerForm.get('emailSociete'); }\n  get telephoneSociete() { return this.registerForm.get('telephoneSociete'); }\n\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n\n  toggleConfirmPasswordVisibility(): void {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n}\n", "<div class=\"register-container\">\n  <mat-card class=\"register-card\">\n    <mat-card-header>\n      <mat-card-title>Inscription</mat-card-title>\n      <mat-card-subtitle>Créez votre compte et votre société</mat-card-subtitle>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n\n        <!-- Section Informations Utilisateur -->\n        <h3 class=\"section-title\">\n          <mat-icon>person</mat-icon>\n          Informations Utilisateur\n        </h3>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Nom complet</mat-label>\n            <input matInput formControlName=\"nom\" placeholder=\"Votre nom complet\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error *ngIf=\"nom?.hasError('required')\">Le nom est requis</mat-error>\n            <mat-error *ngIf=\"nom?.hasError('minlength')\">Le nom doit contenir au moins 2 caractères</mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Nom d'utilisateur</mat-label>\n            <input matInput formControlName=\"userName\" placeholder=\"Nom d'utilisateur\">\n            <mat-icon matSuffix>account_circle</mat-icon>\n            <mat-error *ngIf=\"userName?.hasError('required')\">Le nom d'utilisateur est requis</mat-error>\n            <mat-error *ngIf=\"userName?.hasError('minlength')\">Le nom d'utilisateur doit contenir au moins 3 caractères</mat-error>\n          </mat-form-field>\n        </div>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error *ngIf=\"email?.hasError('required')\">L'email est requis</mat-error>\n          <mat-error *ngIf=\"email?.hasError('email')\">Format d'email invalide</mat-error>\n        </mat-form-field>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Mot de passe</mat-label>\n            <input matInput [type]=\"hidePassword ? 'password' : 'text'\"\n                   formControlName=\"motDePasse\" placeholder=\"Mot de passe\">\n            <button mat-icon-button matSuffix (click)=\"togglePasswordVisibility()\" type=\"button\">\n              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n            </button>\n            <mat-error *ngIf=\"motDePasse?.hasError('required')\">Le mot de passe est requis</mat-error>\n            <mat-error *ngIf=\"motDePasse?.hasError('minlength')\">Le mot de passe doit contenir au moins 6 caractères</mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Confirmer le mot de passe</mat-label>\n            <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                   formControlName=\"confirmMotDePasse\" placeholder=\"Confirmer le mot de passe\">\n            <button mat-icon-button matSuffix (click)=\"toggleConfirmPasswordVisibility()\" type=\"button\">\n              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n            </button>\n            <mat-error *ngIf=\"confirmMotDePasse?.hasError('required')\">La confirmation est requise</mat-error>\n            <mat-error *ngIf=\"confirmMotDePasse?.hasError('passwordMismatch')\">Les mots de passe ne correspondent pas</mat-error>\n          </mat-form-field>\n        </div>\n\n        <!-- Section Informations Société -->\n        <h3 class=\"section-title\">\n          <mat-icon>business</mat-icon>\n          Informations Société\n        </h3>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Nom de la société</mat-label>\n          <input matInput formControlName=\"nomSociete\" placeholder=\"Nom de votre société\">\n          <mat-icon matSuffix>business</mat-icon>\n          <mat-error *ngIf=\"nomSociete?.hasError('required')\">Le nom de la société est requis</mat-error>\n          <mat-error *ngIf=\"nomSociete?.hasError('minlength')\">Le nom doit contenir au moins 2 caractères</mat-error>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse de la société</mat-label>\n          <textarea matInput formControlName=\"adresseSociete\" placeholder=\"Adresse complète de la société\" rows=\"3\"></textarea>\n          <mat-icon matSuffix>location_on</mat-icon>\n          <mat-error *ngIf=\"adresseSociete?.hasError('required')\">L'adresse est requise</mat-error>\n        </mat-form-field>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Matricule fiscal</mat-label>\n            <input matInput formControlName=\"matriculeFiscale\" placeholder=\"Matricule fiscal unique\">\n            <mat-icon matSuffix>receipt</mat-icon>\n            <mat-error *ngIf=\"matriculeFiscale?.hasError('required')\">Le matricule fiscal est requis</mat-error>\n            <mat-error *ngIf=\"matriculeFiscale?.hasError('minlength')\">Le matricule doit contenir au moins 8 caractères</mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Téléphone (optionnel)</mat-label>\n            <input matInput formControlName=\"telephoneSociete\" placeholder=\"Téléphone de la société\">\n            <mat-icon matSuffix>phone</mat-icon>\n          </mat-form-field>\n        </div>\n\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email société (optionnel)</mat-label>\n          <input matInput type=\"email\" formControlName=\"emailSociete\" placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>business_center</mat-icon>\n          <mat-error *ngIf=\"emailSociete?.hasError('email')\">Format d'email invalide</mat-error>\n        </mat-form-field>\n\n        <!-- Message d'erreur -->\n        <div *ngIf=\"error\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ error }}</span>\n        </div>\n\n        <!-- Bouton d'inscription -->\n        <button mat-raised-button color=\"primary\" type=\"submit\"\n                class=\"full-width register-button\" [disabled]=\"loading\">\n          <mat-icon *ngIf=\"loading\">hourglass_empty</mat-icon>\n          <span *ngIf=\"!loading\">Créer le compte</span>\n          <span *ngIf=\"loading\">Création en cours...</span>\n        </button>\n      </form>\n    </mat-card-content>\n\n    <mat-card-actions align=\"center\">\n      <p>Déjà un compte ?\n        <a routerLink=\"/login\" class=\"login-link\">Se connecter</a>\n      </p>\n    </mat-card-actions>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;ICoBvDC,EAAA,CAAAC,cAAA,gBAA6C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC1EH,EAAA,CAAAC,cAAA,gBAA8C;IAAAD,EAAA,CAAAE,MAAA,sDAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOpGH,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC7FH,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,oEAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQzHH,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC7EH,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAW7EH,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC1FH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,+DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUpHH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAClGH,EAAA,CAAAC,cAAA,gBAAmE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAcvHH,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,gDAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAC/FH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,sDAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO3GH,EAAA,CAAAC,cAAA,gBAAwD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQvFH,EAAA,CAAAC,cAAA,gBAA0D;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACpGH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAE,MAAA,4DAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAczHH,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAIxFH,EAAA,CAAAC,cAAA,cAAyC;IAC7BD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAW;;;;;IAMjBP,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpDH,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,2BAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7CH,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD/G3D,OAAM,MAAOK,iBAAiB;EAO5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAN,KAAK,GAAG,EAAE;IACV,KAAAO,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;EAMvB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACpC;;IAGF,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACzC;MACAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACyB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEI,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAACuB,QAAQ,CAAC,CAAC;MAE9C;MACAM,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEM,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACuB,QAAQ,CAAC,CAAC;MAC3CQ,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACtEQ,YAAY,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAACyB,KAAK,CAAC,CAAC;MACtCQ,gBAAgB,EAAE,CAAC,EAAE;KACtB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;EACAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMC,QAAQ,GAAGD,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;IACvC,MAAMC,eAAe,GAAGH,IAAI,CAACE,GAAG,CAAC,mBAAmB,CAAC;IAErD,IAAID,QAAQ,IAAIE,eAAe,IAAIF,QAAQ,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3ED,eAAe,CAACE,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;;IAGnC,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvB,YAAY,CAACwB,OAAO,EAAE;MAC7B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC/B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,KAAK,GAAG,EAAE;IAEf,MAAMsC,SAAS,GAAG,IAAI,CAAC1B,YAAY,CAACoB,KAAK;IAEzC;IACA;IACA,MAAMO,gBAAgB,GAAG;MACvB;MACAzB,GAAG,EAAEwB,SAAS,CAACxB,GAAG;MAClBG,KAAK,EAAEqB,SAAS,CAACrB,KAAK;MACtBC,QAAQ,EAAEoB,SAAS,CAACpB,QAAQ;MAC5BC,UAAU,EAAEmB,SAAS,CAACnB,UAAU;MAEhC;MACAqB,OAAO,EAAE;QACP1B,GAAG,EAAEwB,SAAS,CAACjB,UAAU;QACzBoB,OAAO,EAAEH,SAAS,CAAChB,cAAc;QACjCC,gBAAgB,EAAEe,SAAS,CAACf,gBAAgB;QAC5CN,KAAK,EAAEqB,SAAS,CAACd,YAAY;QAC7BkB,SAAS,EAAEJ,SAAS,CAACb;;KAExB;IAED,IAAI,CAACrB,WAAW,CAACuC,QAAQ,CAACJ,gBAAgB,CAAC,CAACK,SAAS,CAAC;MACpDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvC,OAAO,GAAG,KAAK;QACpB;QACA,IAAI,CAACD,MAAM,CAACM,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;MAC5C,CAAC;MACDX,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACM,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,KAAK,GAAGA,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI,oDAAoD;MAC3F;KACD,CAAC;EACJ;EAEQT,oBAAoBA,CAAA;IAC1BU,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpC,YAAY,CAACqC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMC,OAAO,GAAG,IAAI,CAACxC,YAAY,CAACkB,GAAG,CAACqB,GAAG,CAAC;MAC1CC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;EACA,IAAIvC,GAAGA,CAAA;IAAK,OAAO,IAAI,CAACF,YAAY,CAACkB,GAAG,CAAC,KAAK,CAAC;EAAE;EACjD,IAAIb,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACL,YAAY,CAACkB,GAAG,CAAC,OAAO,CAAC;EAAE;EACrD,IAAIZ,QAAQA,CAAA;IAAK,OAAO,IAAI,CAACN,YAAY,CAACkB,GAAG,CAAC,UAAU,CAAC;EAAE;EAC3D,IAAIX,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACP,YAAY,CAACkB,GAAG,CAAC,YAAY,CAAC;EAAE;EAC/D,IAAIV,iBAAiBA,CAAA;IAAK,OAAO,IAAI,CAACR,YAAY,CAACkB,GAAG,CAAC,mBAAmB,CAAC;EAAE;EAC7E,IAAIT,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACT,YAAY,CAACkB,GAAG,CAAC,YAAY,CAAC;EAAE;EAC/D,IAAIR,cAAcA,CAAA;IAAK,OAAO,IAAI,CAACV,YAAY,CAACkB,GAAG,CAAC,gBAAgB,CAAC;EAAE;EACvE,IAAIP,gBAAgBA,CAAA;IAAK,OAAO,IAAI,CAACX,YAAY,CAACkB,GAAG,CAAC,kBAAkB,CAAC;EAAE;EAC3E,IAAIN,YAAYA,CAAA;IAAK,OAAO,IAAI,CAACZ,YAAY,CAACkB,GAAG,CAAC,cAAc,CAAC;EAAE;EACnE,IAAIL,gBAAgBA,CAAA;IAAK,OAAO,IAAI,CAACb,YAAY,CAACkB,GAAG,CAAC,kBAAkB,CAAC;EAAE;EAE3EwB,wBAAwBA,CAAA;IACtB,IAAI,CAAC/C,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAgD,+BAA+BA,CAAA;IAC7B,IAAI,CAAC/C,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;;;uBAtHWP,iBAAiB,EAAAR,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjE,EAAA,CAAA+D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnE,EAAA,CAAA+D,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjB7D,iBAAiB;MAAA8D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV9B5E,EAAA,CAAAC,cAAA,aAAgC;UAGVD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC5CH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,yDAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAoB;UAG5EH,EAAA,CAAAC,cAAA,uBAAkB;UACiBD,EAAA,CAAA8E,UAAA,sBAAAC,oDAAA;YAAA,OAAYF,GAAA,CAAAnC,QAAA,EAAU;UAAA,EAAC;UAGtD1C,EAAA,CAAAC,cAAA,YAA0B;UACdD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,cAAsB;UAEPD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAgF,SAAA,gBAAsE;UACtEhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAAiF,UAAA,KAAAC,uCAAA,uBAA0E;UAC1ElF,EAAA,CAAAiF,UAAA,KAAAE,uCAAA,uBAAoG;UACtGnF,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAgF,SAAA,gBAA2E;UAC3EhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAiF,UAAA,KAAAG,uCAAA,uBAA6F;UAC7FpF,EAAA,CAAAiF,UAAA,KAAAI,uCAAA,uBAAuH;UACzHrF,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAgF,SAAA,iBAAmF;UACnFhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAiF,UAAA,KAAAK,uCAAA,uBAA6E;UAC7EtF,EAAA,CAAAiF,UAAA,KAAAM,uCAAA,uBAA+E;UACjFvF,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,cAAsB;UAEPD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAgF,SAAA,iBAC+D;UAC/DhF,EAAA,CAAAC,cAAA,kBAAqF;UAAnDD,EAAA,CAAA8E,UAAA,mBAAAU,oDAAA;YAAA,OAASX,GAAA,CAAAhB,wBAAA,EAA0B;UAAA,EAAC;UACpE7D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEzEH,EAAA,CAAAiF,UAAA,KAAAQ,uCAAA,uBAA0F;UAC1FzF,EAAA,CAAAiF,UAAA,KAAAS,uCAAA,uBAAoH;UACtH1F,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChDH,EAAA,CAAAgF,SAAA,iBACmF;UACnFhF,EAAA,CAAAC,cAAA,kBAA4F;UAA1DD,EAAA,CAAA8E,UAAA,mBAAAa,oDAAA;YAAA,OAASd,GAAA,CAAAf,+BAAA,EAAiC;UAAA,EAAC;UAC3E9D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhFH,EAAA,CAAAiF,UAAA,KAAAW,uCAAA,uBAAkG;UAClG5F,EAAA,CAAAiF,UAAA,KAAAY,uCAAA,uBAAqH;UACvH7F,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,aAA0B;UACdD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,mCAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAgF,SAAA,iBAAgF;UAChFhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAAiF,UAAA,KAAAa,uCAAA,uBAA+F;UAC/F9F,EAAA,CAAAiF,UAAA,KAAAc,uCAAA,uBAA2G;UAC7G/F,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,uCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAgF,SAAA,oBAAqH;UACrHhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAiF,UAAA,KAAAe,uCAAA,uBAAyF;UAC3FhG,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,cAAsB;UAEPD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAgF,SAAA,iBAAyF;UACzFhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtCH,EAAA,CAAAiF,UAAA,KAAAgB,uCAAA,uBAAoG;UACpGjG,EAAA,CAAAiF,UAAA,KAAAiB,uCAAA,uBAAuH;UACzHlG,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,uCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAgF,SAAA,iBAAyF;UACzFhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIxCH,EAAA,CAAAC,cAAA,0BAAwD;UAC3CD,EAAA,CAAAE,MAAA,2CAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChDH,EAAA,CAAAgF,SAAA,iBAA8F;UAC9FhF,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAiF,UAAA,KAAAkB,uCAAA,uBAAsF;UACxFnG,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAiF,UAAA,KAAAmB,iCAAA,kBAGM;UAGNpG,EAAA,CAAAC,cAAA,kBACgE;UAC9DD,EAAA,CAAAiF,UAAA,MAAAoB,uCAAA,sBAAoD;UACpDrG,EAAA,CAAAiF,UAAA,MAAAqB,mCAAA,kBAA6C;UAC7CtG,EAAA,CAAAiF,UAAA,MAAAsB,mCAAA,kBAAiD;UACnDvG,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,6BAAiC;UAC5BD,EAAA,CAAAE,MAAA,oCACD;UAAAF,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAxHtDH,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAwG,UAAA,cAAA3B,GAAA,CAAA1D,YAAA,CAA0B;UAadnB,EAAA,CAAAI,SAAA,IAA+B;UAA/BJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAxD,GAAA,kBAAAwD,GAAA,CAAAxD,GAAA,CAAAoF,QAAA,aAA+B;UAC/BzG,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAxD,GAAA,kBAAAwD,GAAA,CAAAxD,GAAA,CAAAoF,QAAA,cAAgC;UAOhCzG,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAApD,QAAA,kBAAAoD,GAAA,CAAApD,QAAA,CAAAgF,QAAA,aAAoC;UACpCzG,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAApD,QAAA,kBAAAoD,GAAA,CAAApD,QAAA,CAAAgF,QAAA,cAAqC;UAQvCzG,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAArD,KAAA,kBAAAqD,GAAA,CAAArD,KAAA,CAAAiF,QAAA,aAAiC;UACjCzG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAArD,KAAA,kBAAAqD,GAAA,CAAArD,KAAA,CAAAiF,QAAA,UAA8B;UAMxBzG,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAA/D,YAAA,uBAA2C;UAG/Cd,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAK,iBAAA,CAAAwE,GAAA,CAAA/D,YAAA,mCAAkD;UAElDd,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAnD,UAAA,kBAAAmD,GAAA,CAAAnD,UAAA,CAAA+E,QAAA,aAAsC;UACtCzG,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAnD,UAAA,kBAAAmD,GAAA,CAAAnD,UAAA,CAAA+E,QAAA,cAAuC;UAKnCzG,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAA9D,mBAAA,uBAAkD;UAGtDf,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAK,iBAAA,CAAAwE,GAAA,CAAA9D,mBAAA,mCAAyD;UAEzDf,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAlD,iBAAA,kBAAAkD,GAAA,CAAAlD,iBAAA,CAAA8E,QAAA,aAA6C;UAC7CzG,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAlD,iBAAA,kBAAAkD,GAAA,CAAAlD,iBAAA,CAAA8E,QAAA,qBAAqD;UAcvDzG,EAAA,CAAAI,SAAA,IAAsC;UAAtCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAjD,UAAA,kBAAAiD,GAAA,CAAAjD,UAAA,CAAA6E,QAAA,aAAsC;UACtCzG,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAjD,UAAA,kBAAAiD,GAAA,CAAAjD,UAAA,CAAA6E,QAAA,cAAuC;UAOvCzG,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAhD,cAAA,kBAAAgD,GAAA,CAAAhD,cAAA,CAAA4E,QAAA,aAA0C;UAQxCzG,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAA/C,gBAAA,kBAAA+C,GAAA,CAAA/C,gBAAA,CAAA2E,QAAA,aAA4C;UAC5CzG,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAA/C,gBAAA,kBAAA+C,GAAA,CAAA/C,gBAAA,CAAA2E,QAAA,cAA6C;UAc/CzG,EAAA,CAAAI,SAAA,IAAqC;UAArCJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAA9C,YAAA,kBAAA8C,GAAA,CAAA9C,YAAA,CAAA0E,QAAA,UAAqC;UAI7CzG,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAtE,KAAA,CAAW;UAO0BP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAwG,UAAA,aAAA3B,GAAA,CAAAhE,OAAA,CAAoB;UAClDb,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAhE,OAAA,CAAa;UACjBb,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAwG,UAAA,UAAA3B,GAAA,CAAAhE,OAAA,CAAc;UACdb,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAwG,UAAA,SAAA3B,GAAA,CAAAhE,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}