{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FactureVenteService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = '/api/factures-vente';\n  }\n  getFacturesVente() {\n    return this.http.get(this.API_URL);\n  }\n  getFactureVente(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  createFactureVente(facture) {\n    return this.http.post(this.API_URL, facture);\n  }\n  updateFactureVente(id, facture) {\n    return this.http.put(`${this.API_URL}/${id}`, facture);\n  }\n  deleteFactureVente(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  getFacturesByClient(clientId) {\n    const params = new HttpParams().set('clientId', clientId);\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  getFacturesByStatut(statut) {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  searchFactures(searchTerm) {\n    const params = new HttpParams().set('search', searchTerm);\n    return this.http.get(`${this.API_URL}/search`, {\n      params\n    });\n  }\n  generatePdf(id) {\n    return this.http.get(`${this.API_URL}/${id}/pdf`, {\n      responseType: 'blob'\n    });\n  }\n  sendByEmail(id, email) {\n    return this.http.post(`${this.API_URL}/${id}/send-email`, {\n      email\n    });\n  }\n  markAsPaid(id, datePaiement) {\n    return this.http.patch(`${this.API_URL}/${id}/mark-paid`, {\n      datePaiement: datePaiement.toISOString()\n    });\n  }\n  markAsSent(id) {\n    return this.http.patch(`${this.API_URL}/${id}/mark-sent`, {});\n  }\n  static {\n    this.ɵfac = function FactureVenteService_Factory(t) {\n      return new (t || FactureVenteService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FactureVenteService,\n      factory: FactureVenteService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "FactureVenteService", "constructor", "http", "API_URL", "getFacturesVente", "get", "getFactureVente", "id", "createFactureVente", "facture", "post", "updateFactureVente", "put", "deleteFactureVente", "delete", "getFacturesByClient", "clientId", "params", "set", "getFacturesByStatut", "statut", "toString", "searchFactures", "searchTerm", "generatePdf", "responseType", "sendByEmail", "email", "markAsPaid", "datePaiement", "patch", "toISOString", "mark<PERSON><PERSON><PERSON>", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\facture-vente.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { FactureVente } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FactureVenteService {\n  private readonly API_URL = '/api/factures-vente';\n\n  constructor(private http: HttpClient) {}\n\n  getFacturesVente(): Observable<FactureVente[]> {\n    return this.http.get<FactureVente[]>(this.API_URL);\n  }\n\n  getFactureVente(id: string): Observable<FactureVente> {\n    return this.http.get<FactureVente>(`${this.API_URL}/${id}`);\n  }\n\n  createFactureVente(facture: Partial<FactureVente>): Observable<FactureVente> {\n    return this.http.post<FactureVente>(this.API_URL, facture);\n  }\n\n  updateFactureVente(id: string, facture: Partial<FactureVente>): Observable<FactureVente> {\n    return this.http.put<FactureVente>(`${this.API_URL}/${id}`, facture);\n  }\n\n  deleteFactureVente(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  getFacturesByClient(clientId: string): Observable<FactureVente[]> {\n    const params = new HttpParams().set('clientId', clientId);\n    return this.http.get<FactureVente[]>(this.API_URL, { params });\n  }\n\n  getFacturesByStatut(statut: number): Observable<FactureVente[]> {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get<FactureVente[]>(this.API_URL, { params });\n  }\n\n  searchFactures(searchTerm: string): Observable<FactureVente[]> {\n    const params = new HttpParams().set('search', searchTerm);\n    return this.http.get<FactureVente[]>(`${this.API_URL}/search`, { params });\n  }\n\n  generatePdf(id: string): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/${id}/pdf`, { \n      responseType: 'blob' \n    });\n  }\n\n  sendByEmail(id: string, email: string): Observable<any> {\n    return this.http.post(`${this.API_URL}/${id}/send-email`, { email });\n  }\n\n  markAsPaid(id: string, datePaiement: Date): Observable<FactureVente> {\n    return this.http.patch<FactureVente>(`${this.API_URL}/${id}/mark-paid`, {\n      datePaiement: datePaiement.toISOString()\n    });\n  }\n\n  markAsSent(id: string): Observable<FactureVente> {\n    return this.http.patch<FactureVente>(`${this.API_URL}/${id}/mark-sent`, {});\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAO7D,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,qBAAqB;EAET;EAEvCC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,CAAC;EACpD;EAEAG,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAe,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EAC7D;EAEAC,kBAAkBA,CAACC,OAA8B;IAC/C,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,IAAI,CAACP,OAAO,EAAEM,OAAO,CAAC;EAC5D;EAEAE,kBAAkBA,CAACJ,EAAU,EAAEE,OAA8B;IAC3D,OAAO,IAAI,CAACP,IAAI,CAACU,GAAG,CAAe,GAAG,IAAI,CAACT,OAAO,IAAII,EAAE,EAAE,EAAEE,OAAO,CAAC;EACtE;EAEAI,kBAAkBA,CAACN,EAAU;IAC3B,OAAO,IAAI,CAACL,IAAI,CAACY,MAAM,CAAO,GAAG,IAAI,CAACX,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;EAEAQ,mBAAmBA,CAACC,QAAgB;IAClC,MAAMC,MAAM,GAAG,IAAIlB,UAAU,EAAE,CAACmB,GAAG,CAAC,UAAU,EAAEF,QAAQ,CAAC;IACzD,OAAO,IAAI,CAACd,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,EAAE;MAAEc;IAAM,CAAE,CAAC;EAChE;EAEAE,mBAAmBA,CAACC,MAAc;IAChC,MAAMH,MAAM,GAAG,IAAIlB,UAAU,EAAE,CAACmB,GAAG,CAAC,QAAQ,EAAEE,MAAM,CAACC,QAAQ,EAAE,CAAC;IAChE,OAAO,IAAI,CAACnB,IAAI,CAACG,GAAG,CAAiB,IAAI,CAACF,OAAO,EAAE;MAAEc;IAAM,CAAE,CAAC;EAChE;EAEAK,cAAcA,CAACC,UAAkB;IAC/B,MAAMN,MAAM,GAAG,IAAIlB,UAAU,EAAE,CAACmB,GAAG,CAAC,QAAQ,EAAEK,UAAU,CAAC;IACzD,OAAO,IAAI,CAACrB,IAAI,CAACG,GAAG,CAAiB,GAAG,IAAI,CAACF,OAAO,SAAS,EAAE;MAAEc;IAAM,CAAE,CAAC;EAC5E;EAEAO,WAAWA,CAACjB,EAAU;IACpB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,MAAM,EAAE;MAChDkB,YAAY,EAAE;KACf,CAAC;EACJ;EAEAC,WAAWA,CAACnB,EAAU,EAAEoB,KAAa;IACnC,OAAO,IAAI,CAACzB,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACP,OAAO,IAAII,EAAE,aAAa,EAAE;MAAEoB;IAAK,CAAE,CAAC;EACtE;EAEAC,UAAUA,CAACrB,EAAU,EAAEsB,YAAkB;IACvC,OAAO,IAAI,CAAC3B,IAAI,CAAC4B,KAAK,CAAe,GAAG,IAAI,CAAC3B,OAAO,IAAII,EAAE,YAAY,EAAE;MACtEsB,YAAY,EAAEA,YAAY,CAACE,WAAW;KACvC,CAAC;EACJ;EAEAC,UAAUA,CAACzB,EAAU;IACnB,OAAO,IAAI,CAACL,IAAI,CAAC4B,KAAK,CAAe,GAAG,IAAI,CAAC3B,OAAO,IAAII,EAAE,YAAY,EAAE,EAAE,CAAC;EAC7E;;;uBA1DWP,mBAAmB,EAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBpC,mBAAmB;MAAAqC,OAAA,EAAnBrC,mBAAmB,CAAAsC,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}