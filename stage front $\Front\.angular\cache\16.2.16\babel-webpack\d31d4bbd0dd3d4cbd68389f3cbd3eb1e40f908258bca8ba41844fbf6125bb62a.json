{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FournisseurService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5251/api/fournisseurs';\n  }\n  getFournisseurs() {\n    return this.http.get(this.API_URL);\n  }\n  getFournisseur(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  createFournisseur(fournisseur) {\n    return this.http.post(this.API_URL, fournisseur);\n  }\n  updateFournisseur(id, fournisseur) {\n    return this.http.put(`${this.API_URL}/${id}`, fournisseur);\n  }\n  deleteFournisseur(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  static {\n    this.ɵfac = function FournisseurService_Factory(t) {\n      return new (t || FournisseurService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FournisseurService,\n      factory: FournisseurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FournisseurService", "constructor", "http", "API_URL", "getFournisseurs", "get", "getFournisseur", "id", "createFournisseur", "<PERSON><PERSON><PERSON><PERSON>", "post", "updateFournisseur", "put", "deleteFournisseur", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\fournisseur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Fournisseur } from 'src/app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FournisseurService {\n  private readonly API_URL = 'http://localhost:5251/api/fournisseurs';\n\n  constructor(private http: HttpClient) {}\n\n  getFournisseurs(): Observable<Fournisseur[]> {\n    return this.http.get<Fournisseur[]>(this.API_URL);\n  }\n\n  getFournisseur(id: string): Observable<Fournisseur> {\n    return this.http.get<Fournisseur>(`${this.API_URL}/${id}`);\n  }\n\n  createFournisseur(fournisseur: Partial<Fournisseur>): Observable<Fournisseur> {\n    return this.http.post<Fournisseur>(this.API_URL, fournisseur);\n  }\n\n  updateFournisseur(id: string, fournisseur: Partial<Fournisseur>): Observable<Fournisseur> {\n    return this.http.put<Fournisseur>(`${this.API_URL}/${id}`, fournisseur);\n  }\n\n  deleteFournisseur(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Ces endpoints n'existent pas dans le backend\n  // Seuls les CRUD de base sont disponibles\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,wCAAwC;EAE5B;EAEvCC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAgB,IAAI,CAACF,OAAO,CAAC;EACnD;EAEAG,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAc,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EAC5D;EAEAC,iBAAiBA,CAACC,WAAiC;IACjD,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAc,IAAI,CAACP,OAAO,EAAEM,WAAW,CAAC;EAC/D;EAEAE,iBAAiBA,CAACJ,EAAU,EAAEE,WAAiC;IAC7D,OAAO,IAAI,CAACP,IAAI,CAACU,GAAG,CAAc,GAAG,IAAI,CAACT,OAAO,IAAII,EAAE,EAAE,EAAEE,WAAW,CAAC;EACzE;EAEAI,iBAAiBA,CAACN,EAAU;IAC1B,OAAO,IAAI,CAACL,IAAI,CAACY,MAAM,CAAO,GAAG,IAAI,CAACX,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;;;uBAvBWP,kBAAkB,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBlB,kBAAkB;MAAAmB,OAAA,EAAlBnB,kBAAkB,CAAAoB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}