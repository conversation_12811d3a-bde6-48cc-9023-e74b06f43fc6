{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ClientsComponent {\n  static {\n    this.ɵfac = function ClientsComponent_Factory(t) {\n      return new (t || ClientsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientsComponent,\n      selectors: [[\"app-clients\"]],\n      decls: 2,\n      vars: 0,\n      template: function ClientsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"clients works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ClientsComponent", "selectors", "decls", "vars", "template", "ClientsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\clients\\clients.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\clients\\clients.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-clients',\n  templateUrl: './clients.component.html',\n  styleUrls: ['./clients.component.css']\n})\nexport class ClientsComponent {\n\n}\n", "<p>clients works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}