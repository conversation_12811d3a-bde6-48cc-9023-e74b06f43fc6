{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nfunction LoginComponent_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" L'email est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format d'email invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe doit contenir au moins 6 caract\\u00E8res \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.error);\n  }\n}\nfunction LoginComponent_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, router) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n      this.error = '';\n      this.hidePassword = true;\n    }\n    ngOnInit() {\n      // Rediriger si déjà connecté\n      if (this.authService.isAuthenticated()) {\n        this.redirectBasedOnRole();\n        return;\n      }\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        motDePasse: ['', [Validators.required, Validators.minLength(6)]]\n      });\n    }\n    onSubmit() {\n      if (this.loginForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      const {\n        email,\n        motDePasse\n      } = this.loginForm.value;\n      this.authService.login(email, motDePasse).subscribe({\n        next: response => {\n          this.loading = false;\n          this.redirectBasedOnRole();\n        },\n        error: error => {\n          this.loading = false;\n          this.error = error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.';\n        }\n      });\n    }\n    redirectBasedOnRole() {\n      if (this.authService.isAdmin()) {\n        this.router.navigate(['/admin-dashboard']);\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    // Getters pour faciliter l'accès aux contrôles dans le template\n    get email() {\n      return this.loginForm.get('email');\n    }\n    get motDePasse() {\n      return this.loginForm.get('motDePasse');\n    }\n    togglePasswordVisibility() {\n      this.hidePassword = !this.hidePassword;\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 36,\n        vars: 14,\n        consts: [[1, \"login-container\"], [1, \"login-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"motDePasse\", \"placeholder\", \"Votre mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [\"align\", \"center\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"error-message\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Connexion\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n            i0.ɵɵtext(6, \"Connectez-vous \\u00E0 votre compte\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_8_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(9, \"mat-form-field\", 3)(10, \"mat-label\");\n            i0.ɵɵtext(11, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"input\", 4);\n            i0.ɵɵelementStart(13, \"mat-icon\", 5);\n            i0.ɵɵtext(14, \"email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(15, LoginComponent_mat_error_15_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵtemplate(16, LoginComponent_mat_error_16_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"mat-form-field\", 3)(18, \"mat-label\");\n            i0.ɵɵtext(19, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 7);\n            i0.ɵɵelementStart(21, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_21_listener() {\n              return ctx.togglePasswordVisibility();\n            });\n            i0.ɵɵelementStart(22, \"mat-icon\");\n            i0.ɵɵtext(23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(24, LoginComponent_mat_error_24_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(26, LoginComponent_div_26_Template, 5, 1, \"div\", 9);\n            i0.ɵɵelementStart(27, \"button\", 10);\n            i0.ɵɵtemplate(28, LoginComponent_mat_icon_28_Template, 2, 0, \"mat-icon\", 6);\n            i0.ɵɵtemplate(29, LoginComponent_span_29_Template, 2, 0, \"span\", 6);\n            i0.ɵɵtemplate(30, LoginComponent_span_30_Template, 2, 0, \"span\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"mat-card-actions\", 11)(32, \"p\");\n            i0.ɵɵtext(33, \"Pas encore de compte ? \");\n            i0.ɵɵelementStart(34, \"a\", 12);\n            i0.ɵɵtext(35, \"S'inscrire\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"required\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"email\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"required\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"minlength\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        styles: [\".login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:20px}.login-card[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px;box-shadow:0 8px 32px #0000001a;border-radius:16px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.login-button[_ngcontent-%COMP%]{height:48px;font-size:16px;margin-top:16px}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;color:#f44336;margin-bottom:16px;padding:8px;background-color:#ffebee;border-radius:4px;border-left:4px solid #f44336}.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.register-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.register-link[_ngcontent-%COMP%]:hover{text-decoration:underline}mat-card-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px}mat-card-title[_ngcontent-%COMP%]{font-size:24px;font-weight:600;color:#333}mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:8px}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}