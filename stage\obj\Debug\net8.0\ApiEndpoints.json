[{"ContainingType": "stage.Controller<PERSON>.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "stage.DTOs.Auth.UserLoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controller<PERSON>.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "stage.Controller<PERSON>.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "stage.DTOs.Auth.UserRegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.ClientsController", "Method": "GetClients", "RelativePath": "api/Clients", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.Client, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.ClientsController", "Method": "PostClient", "RelativePath": "api/Clients", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "client", "Type": "stage.Models.Client", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Client", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.ClientsController", "Method": "GetClient", "RelativePath": "api/Clients/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Client", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.ClientsController", "Method": "PutClient", "RelativePath": "api/Clients/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "client", "Type": "stage.Models.Client", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.ClientsController", "Method": "DeleteClient", "RelativePath": "api/Clients/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "GetDocuments", "RelativePath": "api/Documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.Document, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "PostDocument", "RelativePath": "api/Documents", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "document", "Type": "stage.Models.Document", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Document", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "GetDocument", "RelativePath": "api/Documents/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Document", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "PutDocument", "RelativePath": "api/Documents/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "document", "Type": "stage.Models.Document", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "DeleteDocument", "RelativePath": "api/Documents/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "ApproveDocument", "RelativePath": "api/Documents/{id}/approve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "commentaires", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.DocumentsController", "Method": "RejectDocument", "RelativePath": "api/Documents/{id}/reject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "commentaires", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "GetFacturesAchat", "RelativePath": "api/factures-achat", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.FactureAchat, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "PostFactureAchat", "RelativePath": "api/factures-achat", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "numero", "Type": "System.String", "IsRequired": false}, {"Name": "date", "Type": "System.DateTime", "IsRequired": false}, {"Name": "montant", "Type": "System.Decimal", "IsRequired": false}, {"Name": "fournisseurId", "Type": "System.Guid", "IsRequired": false}, {"Name": "statut", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "stage.Models.FactureAchat", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "GetFactureAchat", "RelativePath": "api/factures-achat/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.FactureAchat", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "PutFactureAchat", "RelativePath": "api/factures-achat/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "facture", "Type": "stage.Models.FactureAchat", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "DeleteFactureAchat", "RelativePath": "api/factures-achat/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesAchatController", "Method": "DownloadFactureFile", "RelativePath": "api/factures-achat/{id}/download-file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "GetFacturesVente", "RelativePath": "api/factures-vente", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.FactureVente, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "PostFactureVente", "RelativePath": "api/factures-vente", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "facture", "Type": "stage.Models.FactureVente", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.FactureVente", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "GetFactureVente", "RelativePath": "api/factures-vente/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.FactureVente", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "PutFactureVente", "RelativePath": "api/factures-vente/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "facture", "Type": "stage.Models.FactureVente", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "DeleteFactureVente", "RelativePath": "api/factures-vente/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FacturesVenteController", "Method": "ChangeStatus", "RelativePath": "api/factures-vente/{id}/change-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "nouveauStatut", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FournisseursController", "Method": "GetFournisseurs", "RelativePath": "api/Fournis<PERSON>urs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.Fournisseur, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FournisseursController", "Method": "PostFournisseur", "RelativePath": "api/Fournis<PERSON>urs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "stage.Models.Fournisseur", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Fournisseur", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FournisseursController", "Method": "GetFournisseur", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.Fournisseur", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.FournisseursController", "Method": "PutFournisseur", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "stage.Models.Fournisseur", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.FournisseursController", "Method": "DeleteFournisseur", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "GetNotifications", "RelativePath": "api/Notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeRead", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[stage.Models.Notification, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "DeleteNotification", "RelativePath": "api/Notifications/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "MarkAsRead", "RelativePath": "api/Notifications/{id}/mark-read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "MarkAllAsRead", "RelativePath": "api/Notifications/mark-all-read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "GetUnreadNotifications", "RelativePath": "api/Notifications/unread", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[stage.Models.Notification, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.NotificationsController", "Method": "GetUnreadCount", "RelativePath": "api/Notifications/unread-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "GetAll", "RelativePath": "api/RetenueSource", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.Models.RetenueSource, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "GetById", "RelativePath": "api/RetenueSource/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.RetenueSource", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "Update", "RelativePath": "api/RetenueSource/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "retenue", "Type": "stage.Models.RetenueSource", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.Models.RetenueSource", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "Delete", "RelativePath": "api/RetenueSource/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "DownloadRetenueFile", "RelativePath": "api/RetenueSource/{id}/download-file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.RetenueSourceController", "Method": "CreateFromUpload", "RelativePath": "api/RetenueSource/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "commentaires", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "stage.Models.RetenueSource", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.SocietesController", "Method": "GetSociete", "RelativePath": "api/Societes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "stage.Models.Societe", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.SocietesController", "Method": "PutSociete", "RelativePath": "api/Societes", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "societe", "Type": "stage.Models.Societe", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.UtilisateursController", "Method": "GetUtilisateurs", "RelativePath": "api/Utilisateurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.DTOs.User.UtilisateurDto, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.UtilisateursController", "Method": "PostUtilisateur", "RelativePath": "api/Utilisateurs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "utilisateurCreateDto", "Type": "stage.DTOs.User.UtilisateurCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.DTOs.User.UtilisateurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.UtilisateursController", "Method": "GetUtilisateur", "RelativePath": "api/Utilisateurs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "stage.DTOs.User.UtilisateurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "stage.Controllers.UtilisateursController", "Method": "PutUtilisateur", "RelativePath": "api/Utilisateurs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "utilisateurUpdateDto", "Type": "stage.DTOs.User.UtilisateurUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.UtilisateursController", "Method": "DeleteUtilisateur", "RelativePath": "api/Utilisateurs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "stage.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[stage.WeatherForecast, stage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]