{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\models\\facture-vente.model.ts"], "sourcesContent": ["import { FactureBase } from './facture-base.model';\nimport { Client } from './client.model';\n\nexport interface FactureVente extends FactureBase {\n  clientId: string;\n  client?: Client;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}