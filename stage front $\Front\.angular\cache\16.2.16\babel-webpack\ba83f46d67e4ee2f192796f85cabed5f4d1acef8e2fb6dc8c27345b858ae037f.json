{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { StatutDocument, TypeDemandeDocument } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/document.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nexport class AdminDocumentsComponent {\n  constructor(documentService, authService, formBuilder, snackBar) {\n    this.documentService = documentService;\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.documents = [];\n    this.filteredDocuments = [];\n    this.typesDocuments = [];\n    this.loading = false;\n    this.searchTerm = '';\n    this.selectedStatut = null;\n    this.selectedType = null;\n    this.showTypeForm = false;\n    // Énumérations pour le template\n    this.StatutDocument = StatutDocument;\n    this.TypeDemandeDocument = TypeDemandeDocument;\n    this.statutOptions = [{\n      value: StatutDocument.EnAttente,\n      label: 'En attente'\n    }, {\n      value: StatutDocument.Approuve,\n      label: 'Approuvé'\n    }, {\n      value: StatutDocument.Rejete,\n      label: 'Rejeté'\n    }];\n    this.typeDemandeOptions = [{\n      value: TypeDemandeDocument.AttestationTravail,\n      label: 'Attestation de travail'\n    }, {\n      value: TypeDemandeDocument.AttestationStage,\n      label: 'Attestation de stage'\n    }, {\n      value: TypeDemandeDocument.CertificatTravail,\n      label: 'Certificat de travail'\n    }, {\n      value: TypeDemandeDocument.Autre,\n      label: 'Autre'\n    }];\n    // Colonnes à afficher dans le tableau\n    this.displayedColumns = ['titre', 'typeDemande', 'typeDocument', 'utilisateurDemandeur', 'dateCreation', 'statut', 'actions'];\n  }\n  ngOnInit() {\n    // Vérifier les permissions - seuls les admins peuvent gérer les documents\n    if (!this.authService.canManageDocuments()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', {\n        duration: 3000\n      });\n      return;\n    }\n    this.initializeTypeForm();\n    this.loadTypesDocuments();\n    this.loadDocuments();\n  }\n  initializeTypeForm() {\n    this.typeDocumentForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      description: ['']\n    });\n  }\n  loadTypesDocuments() {\n    this.documentService.getTypesDocuments().subscribe({\n      next: types => {\n        this.typesDocuments = types;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des types de documents');\n      }\n    });\n  }\n  loadDocuments() {\n    this.loading = true;\n    this.documentService.getDocuments().subscribe({\n      next: documents => {\n        this.documents = documents;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des documents');\n        this.loading = false;\n      }\n    });\n  }\n  applyFilters() {\n    this.filteredDocuments = this.documents.filter(document => {\n      const matchesSearch = !this.searchTerm || document.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || document.utilisateurDemandeur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesStatut = this.selectedStatut === null || document.statut === this.selectedStatut;\n      const matchesType = this.selectedType === null || document.typeDocumentId === this.selectedType;\n      return matchesSearch && matchesStatut && matchesType;\n    });\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  onStatutFilterChange() {\n    this.applyFilters();\n  }\n  onTypeFilterChange() {\n    this.applyFilters();\n  }\n  // Gestion des types de documents\n  showTypeDocumentForm() {\n    this.typeDocumentForm.reset();\n    this.showTypeForm = true;\n  }\n  cancelTypeForm() {\n    this.showTypeForm = false;\n    this.typeDocumentForm.reset();\n  }\n  onSubmitType() {\n    if (this.typeDocumentForm.invalid) {\n      this.markFormGroupTouched(this.typeDocumentForm);\n      return;\n    }\n    const typeData = this.typeDocumentForm.value;\n    this.documentService.createTypeDocument(typeData).subscribe({\n      next: type => {\n        this.typesDocuments.push(type);\n        this.showSuccess('Type de document créé avec succès');\n        this.cancelTypeForm();\n      },\n      error: error => {\n        this.showError('Erreur lors de la création du type de document');\n      }\n    });\n  }\n  deleteTypeDocument(type) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le type \"${type.nom}\" ?`)) {\n      this.documentService.deleteTypeDocument(type.id).subscribe({\n        next: () => {\n          this.typesDocuments = this.typesDocuments.filter(t => t.id !== type.id);\n          this.showSuccess('Type de document supprimé avec succès');\n        },\n        error: error => {\n          this.showError('Erreur lors de la suppression du type de document');\n        }\n      });\n    }\n  }\n  // Gestion des documents\n  approveDocument(document) {\n    this.changeDocumentStatus(document, StatutDocument.Approuve, 'approuver');\n  }\n  rejectDocument(document) {\n    this.changeDocumentStatus(document, StatutDocument.Rejete, 'rejeter');\n  }\n  changeDocumentStatus(document, newStatut, action) {\n    if (confirm(`Êtes-vous sûr de vouloir ${action} le document \"${document.titre}\" ?`)) {\n      this.loading = true;\n      this.documentService.updateDocumentStatus(document.id, newStatut).subscribe({\n        next: updatedDocument => {\n          const index = this.documents.findIndex(d => d.id === document.id);\n          if (index !== -1) {\n            this.documents[index] = updatedDocument;\n            this.applyFilters();\n          }\n          this.showSuccess(`Document ${action === 'approuver' ? 'approuvé' : 'rejeté'} avec succès`);\n          this.loading = false;\n          // Notification automatique à l'utilisateur (géré côté backend)\n        },\n\n        error: error => {\n          this.showError(`Erreur lors de la modification du statut`);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  deleteDocument(document) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le document \"${document.titre}\" ?`)) {\n      this.loading = true;\n      this.documentService.deleteDocument(document.id).subscribe({\n        next: () => {\n          this.documents = this.documents.filter(d => d.id !== document.id);\n          this.applyFilters();\n          this.showSuccess('Document supprimé avec succès');\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la suppression du document');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  getStatutLabel(statut) {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n  getStatutClass(statut) {\n    switch (statut) {\n      case StatutDocument.EnAttente:\n        return 'statut-attente';\n      case StatutDocument.Approuve:\n        return 'statut-approuve';\n      case StatutDocument.Rejete:\n        return 'statut-rejete';\n      default:\n        return '';\n    }\n  }\n  getTypeDemandeLabel(typeDemande) {\n    const option = this.typeDemandeOptions.find(opt => opt.value === typeDemande);\n    return option ? option.label : 'Inconnu';\n  }\n  getTypeDocumentName(typeDocumentId) {\n    const type = this.typesDocuments.find(t => t.id === typeDocumentId);\n    return type ? type.nom : 'Type inconnu';\n  }\n  canApprove(document) {\n    return document.statut === StatutDocument.EnAttente;\n  }\n  canReject(document) {\n    return document.statut === StatutDocument.EnAttente;\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() {\n    return this.typeDocumentForm.get('nom');\n  }\n  get description() {\n    return this.typeDocumentForm.get('description');\n  }\n  static {\n    this.ɵfac = function AdminDocumentsComponent_Factory(t) {\n      return new (t || AdminDocumentsComponent)(i0.ɵɵdirectiveInject(i1.DocumentService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDocumentsComponent,\n      selectors: [[\"app-admin-documents\"]],\n      decls: 2,\n      vars: 0,\n      template: function AdminDocumentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"admin-documents works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "StatutDocument", "TypeDemandeDocument", "AdminDocumentsComponent", "constructor", "documentService", "authService", "formBuilder", "snackBar", "documents", "filteredDocuments", "typesDocuments", "loading", "searchTerm", "selectedStatut", "selectedType", "showTypeForm", "statutOptions", "value", "EnAttente", "label", "Approuve", "<PERSON><PERSON><PERSON>", "typeDemandeOptions", "AttestationTravail", "AttestationStage", "CertificatTravail", "<PERSON><PERSON>", "displayedColumns", "ngOnInit", "canManageDocuments", "open", "duration", "initializeTypeForm", "loadTypesDocuments", "loadDocuments", "typeDocumentForm", "group", "nom", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "getTypesDocuments", "subscribe", "next", "types", "error", "showError", "getDocuments", "applyFilters", "filter", "document", "matchesSearch", "titre", "toLowerCase", "includes", "utilisateurDemandeur", "matchesStatut", "statut", "matchesType", "typeDocumentId", "onSearchChange", "onStatutFilterChange", "onTypeFilterChange", "showTypeDocumentForm", "reset", "cancelTypeForm", "onSubmitType", "invalid", "markFormGroupTouched", "typeData", "createTypeDocument", "type", "push", "showSuccess", "deleteTypeDocument", "confirm", "id", "t", "approveDocument", "changeDocumentStatus", "rejectDocument", "newStatut", "action", "updateDocumentStatus", "updatedDocument", "index", "findIndex", "d", "deleteDocument", "getStatutLabel", "option", "find", "opt", "getStatutClass", "getTypeDemandeLabel", "typeDemande", "getTypeDocumentName", "canApprove", "canReject", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "panelClass", "i0", "ɵɵdirectiveInject", "i1", "DocumentService", "i2", "AuthService", "i3", "FormBuilder", "i4", "MatSnackBar", "selectors", "decls", "vars", "template", "AdminDocumentsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\admin-documents\\admin-documents.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\admin-documents\\admin-documents.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { DocumentService } from '../../services/document.service';\nimport { AuthService } from '../../services/auth.service';\nimport { Document, TypeDocument, StatutDocument, TypeDemandeDocument } from '../models';\n\n@Component({\n  selector: 'app-admin-documents',\n  templateUrl: './admin-documents.component.html',\n  styleUrls: ['./admin-documents.component.css']\n})\nexport class AdminDocumentsComponent implements OnInit {\n  documents: Document[] = [];\n  filteredDocuments: Document[] = [];\n  typesDocuments: TypeDocument[] = [];\n  loading = false;\n  searchTerm = '';\n  selectedStatut: StatutDocument | null = null;\n  selectedType: string | null = null;\n\n  // Formulaire pour créer un type de document\n  typeDocumentForm!: FormGroup;\n  showTypeForm = false;\n\n  // Énumérations pour le template\n  StatutDocument = StatutDocument;\n  TypeDemandeDocument = TypeDemandeDocument;\n\n  statutOptions = [\n    { value: StatutDocument.EnAttente, label: 'En attente' },\n    { value: StatutDocument.Approuve, label: 'Approuvé' },\n    { value: StatutDocument.Rejete, label: 'Rejeté' }\n  ];\n\n  typeDemandeOptions = [\n    { value: TypeDemandeDocument.AttestationTravail, label: 'Attestation de travail' },\n    { value: TypeDemandeDocument.AttestationStage, label: 'Attestation de stage' },\n    { value: TypeDemandeDocument.CertificatTravail, label: 'Certificat de travail' },\n    { value: TypeDemandeDocument.Autre, label: 'Autre' }\n  ];\n\n  // Colonnes à afficher dans le tableau\n  displayedColumns: string[] = ['titre', 'typeDemande', 'typeDocument', 'utilisateurDemandeur', 'dateCreation', 'statut', 'actions'];\n\n  constructor(\n    private documentService: DocumentService,\n    private authService: AuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Vérifier les permissions - seuls les admins peuvent gérer les documents\n    if (!this.authService.canManageDocuments()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.initializeTypeForm();\n    this.loadTypesDocuments();\n    this.loadDocuments();\n  }\n\n  initializeTypeForm(): void {\n    this.typeDocumentForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      description: ['']\n    });\n  }\n\n  loadTypesDocuments(): void {\n    this.documentService.getTypesDocuments().subscribe({\n      next: (types: TypeDocument[]) => {\n        this.typesDocuments = types;\n      },\n      error: (error: any) => {\n        this.showError('Erreur lors du chargement des types de documents');\n      }\n    });\n  }\n\n  loadDocuments(): void {\n    this.loading = true;\n    this.documentService.getDocuments().subscribe({\n      next: (documents) => {\n        this.documents = documents;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors du chargement des documents');\n        this.loading = false;\n      }\n    });\n  }\n\n  applyFilters(): void {\n    this.filteredDocuments = this.documents.filter(document => {\n      const matchesSearch = !this.searchTerm ||\n        document.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        (document.utilisateurDemandeur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));\n\n      const matchesStatut = this.selectedStatut === null || document.statut === this.selectedStatut;\n      const matchesType = this.selectedType === null || document.typeDocumentId === this.selectedType;\n\n      return matchesSearch && matchesStatut && matchesType;\n    });\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  onStatutFilterChange(): void {\n    this.applyFilters();\n  }\n\n  onTypeFilterChange(): void {\n    this.applyFilters();\n  }\n\n  // Gestion des types de documents\n  showTypeDocumentForm(): void {\n    this.typeDocumentForm.reset();\n    this.showTypeForm = true;\n  }\n\n  cancelTypeForm(): void {\n    this.showTypeForm = false;\n    this.typeDocumentForm.reset();\n  }\n\n  onSubmitType(): void {\n    if (this.typeDocumentForm.invalid) {\n      this.markFormGroupTouched(this.typeDocumentForm);\n      return;\n    }\n\n    const typeData = this.typeDocumentForm.value;\n    this.documentService.createTypeDocument(typeData).subscribe({\n      next: (type) => {\n        this.typesDocuments.push(type);\n        this.showSuccess('Type de document créé avec succès');\n        this.cancelTypeForm();\n      },\n      error: (error) => {\n        this.showError('Erreur lors de la création du type de document');\n      }\n    });\n  }\n\n  deleteTypeDocument(type: TypeDocument): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le type \"${type.nom}\" ?`)) {\n      this.documentService.deleteTypeDocument(type.id).subscribe({\n        next: () => {\n          this.typesDocuments = this.typesDocuments.filter(t => t.id !== type.id);\n          this.showSuccess('Type de document supprimé avec succès');\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la suppression du type de document');\n        }\n      });\n    }\n  }\n\n  // Gestion des documents\n  approveDocument(document: Document): void {\n    this.changeDocumentStatus(document, StatutDocument.Approuve, 'approuver');\n  }\n\n  rejectDocument(document: Document): void {\n    this.changeDocumentStatus(document, StatutDocument.Rejete, 'rejeter');\n  }\n\n  changeDocumentStatus(document: Document, newStatut: StatutDocument, action: string): void {\n    if (confirm(`Êtes-vous sûr de vouloir ${action} le document \"${document.titre}\" ?`)) {\n      this.loading = true;\n      this.documentService.updateDocumentStatus(document.id, newStatut).subscribe({\n        next: (updatedDocument) => {\n          const index = this.documents.findIndex(d => d.id === document.id);\n          if (index !== -1) {\n            this.documents[index] = updatedDocument;\n            this.applyFilters();\n          }\n          this.showSuccess(`Document ${action === 'approuver' ? 'approuvé' : 'rejeté'} avec succès`);\n          this.loading = false;\n\n          // Notification automatique à l'utilisateur (géré côté backend)\n        },\n        error: (error) => {\n          this.showError(`Erreur lors de la modification du statut`);\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  deleteDocument(document: Document): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le document \"${document.titre}\" ?`)) {\n      this.loading = true;\n      this.documentService.deleteDocument(document.id).subscribe({\n        next: () => {\n          this.documents = this.documents.filter(d => d.id !== document.id);\n          this.applyFilters();\n          this.showSuccess('Document supprimé avec succès');\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la suppression du document');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  getStatutLabel(statut: StatutDocument): string {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n\n  getStatutClass(statut: StatutDocument): string {\n    switch (statut) {\n      case StatutDocument.EnAttente:\n        return 'statut-attente';\n      case StatutDocument.Approuve:\n        return 'statut-approuve';\n      case StatutDocument.Rejete:\n        return 'statut-rejete';\n      default:\n        return '';\n    }\n  }\n\n  getTypeDemandeLabel(typeDemande: TypeDemandeDocument): string {\n    const option = this.typeDemandeOptions.find(opt => opt.value === typeDemande);\n    return option ? option.label : 'Inconnu';\n  }\n\n  getTypeDocumentName(typeDocumentId: string): string {\n    const type = this.typesDocuments.find(t => t.id === typeDocumentId);\n    return type ? type.nom : 'Type inconnu';\n  }\n\n  canApprove(document: Document): boolean {\n    return document.statut === StatutDocument.EnAttente;\n  }\n\n  canReject(document: Document): boolean {\n    return document.statut === StatutDocument.EnAttente;\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() { return this.typeDocumentForm.get('nom'); }\n  get description() { return this.typeDocumentForm.get('description'); }\n}\n", "<p>admin-documents works!</p>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,SAAiCC,cAAc,EAAEC,mBAAmB,QAAQ,WAAW;;;;;;AAOvF,OAAM,MAAOC,uBAAuB;EAiClCC,YACUC,eAAgC,EAChCC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IApClB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,iBAAiB,GAAe,EAAE;IAClC,KAAAC,cAAc,GAAmB,EAAE;IACnC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,cAAc,GAA0B,IAAI;IAC5C,KAAAC,YAAY,GAAkB,IAAI;IAIlC,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAf,cAAc,GAAGA,cAAc;IAC/B,KAAAC,mBAAmB,GAAGA,mBAAmB;IAEzC,KAAAe,aAAa,GAAG,CACd;MAAEC,KAAK,EAAEjB,cAAc,CAACkB,SAAS;MAAEC,KAAK,EAAE;IAAY,CAAE,EACxD;MAAEF,KAAK,EAAEjB,cAAc,CAACoB,QAAQ;MAAED,KAAK,EAAE;IAAU,CAAE,EACrD;MAAEF,KAAK,EAAEjB,cAAc,CAACqB,MAAM;MAAEF,KAAK,EAAE;IAAQ,CAAE,CAClD;IAED,KAAAG,kBAAkB,GAAG,CACnB;MAAEL,KAAK,EAAEhB,mBAAmB,CAACsB,kBAAkB;MAAEJ,KAAK,EAAE;IAAwB,CAAE,EAClF;MAAEF,KAAK,EAAEhB,mBAAmB,CAACuB,gBAAgB;MAAEL,KAAK,EAAE;IAAsB,CAAE,EAC9E;MAAEF,KAAK,EAAEhB,mBAAmB,CAACwB,iBAAiB;MAAEN,KAAK,EAAE;IAAuB,CAAE,EAChF;MAAEF,KAAK,EAAEhB,mBAAmB,CAACyB,KAAK;MAAEP,KAAK,EAAE;IAAO,CAAE,CACrD;IAED;IACA,KAAAQ,gBAAgB,GAAa,CAAC,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,sBAAsB,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,CAAC;EAO/H;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACvB,WAAW,CAACwB,kBAAkB,EAAE,EAAE;MAC1C,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC,kDAAkD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpG;;IAGF,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAF,kBAAkBA,CAAA;IAChB,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAAC7B,WAAW,CAAC8B,KAAK,CAAC;MAC7CC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACuC,QAAQ,EAAEvC,UAAU,CAACwC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,WAAW,EAAE,CAAC,EAAE;KACjB,CAAC;EACJ;EAEAP,kBAAkBA,CAAA;IAChB,IAAI,CAAC7B,eAAe,CAACqC,iBAAiB,EAAE,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,KAAqB,IAAI;QAC9B,IAAI,CAAClC,cAAc,GAAGkC,KAAK;MAC7B,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACC,SAAS,CAAC,kDAAkD,CAAC;MACpE;KACD,CAAC;EACJ;EAEAZ,aAAaA,CAAA;IACX,IAAI,CAACvB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,eAAe,CAAC2C,YAAY,EAAE,CAACL,SAAS,CAAC;MAC5CC,IAAI,EAAGnC,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACwC,YAAY,EAAE;QACnB,IAAI,CAACrC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,yCAAyC,CAAC;QACzD,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqC,YAAYA,CAAA;IACV,IAAI,CAACvC,iBAAiB,GAAG,IAAI,CAACD,SAAS,CAACyC,MAAM,CAACC,QAAQ,IAAG;MACxD,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACvC,UAAU,IACpCsC,QAAQ,CAACE,KAAK,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC1C,UAAU,CAACyC,WAAW,EAAE,CAAC,IACnEH,QAAQ,CAACK,oBAAoB,EAAElB,GAAG,CAACgB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC1C,UAAU,CAACyC,WAAW,EAAE,CAAE;MAE5F,MAAMG,aAAa,GAAG,IAAI,CAAC3C,cAAc,KAAK,IAAI,IAAIqC,QAAQ,CAACO,MAAM,KAAK,IAAI,CAAC5C,cAAc;MAC7F,MAAM6C,WAAW,GAAG,IAAI,CAAC5C,YAAY,KAAK,IAAI,IAAIoC,QAAQ,CAACS,cAAc,KAAK,IAAI,CAAC7C,YAAY;MAE/F,OAAOqC,aAAa,IAAIK,aAAa,IAAIE,WAAW;IACtD,CAAC,CAAC;EACJ;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACZ,YAAY,EAAE;EACrB;EAEAa,oBAAoBA,CAAA;IAClB,IAAI,CAACb,YAAY,EAAE;EACrB;EAEAc,kBAAkBA,CAAA;IAChB,IAAI,CAACd,YAAY,EAAE;EACrB;EAEA;EACAe,oBAAoBA,CAAA;IAClB,IAAI,CAAC5B,gBAAgB,CAAC6B,KAAK,EAAE;IAC7B,IAAI,CAACjD,YAAY,GAAG,IAAI;EAC1B;EAEAkD,cAAcA,CAAA;IACZ,IAAI,CAAClD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACoB,gBAAgB,CAAC6B,KAAK,EAAE;EAC/B;EAEAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/B,gBAAgB,CAACgC,OAAO,EAAE;MACjC,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACjC,gBAAgB,CAAC;MAChD;;IAGF,MAAMkC,QAAQ,GAAG,IAAI,CAAClC,gBAAgB,CAAClB,KAAK;IAC5C,IAAI,CAACb,eAAe,CAACkE,kBAAkB,CAACD,QAAQ,CAAC,CAAC3B,SAAS,CAAC;MAC1DC,IAAI,EAAG4B,IAAI,IAAI;QACb,IAAI,CAAC7D,cAAc,CAAC8D,IAAI,CAACD,IAAI,CAAC;QAC9B,IAAI,CAACE,WAAW,CAAC,mCAAmC,CAAC;QACrD,IAAI,CAACR,cAAc,EAAE;MACvB,CAAC;MACDpB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,gDAAgD,CAAC;MAClE;KACD,CAAC;EACJ;EAEA4B,kBAAkBA,CAACH,IAAkB;IACnC,IAAII,OAAO,CAAC,+CAA+CJ,IAAI,CAAClC,GAAG,KAAK,CAAC,EAAE;MACzE,IAAI,CAACjC,eAAe,CAACsE,kBAAkB,CAACH,IAAI,CAACK,EAAE,CAAC,CAAClC,SAAS,CAAC;QACzDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACjC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACuC,MAAM,CAAC4B,CAAC,IAAIA,CAAC,CAACD,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;UACvE,IAAI,CAACH,WAAW,CAAC,uCAAuC,CAAC;QAC3D,CAAC;QACD5B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,mDAAmD,CAAC;QACrE;OACD,CAAC;;EAEN;EAEA;EACAgC,eAAeA,CAAC5B,QAAkB;IAChC,IAAI,CAAC6B,oBAAoB,CAAC7B,QAAQ,EAAElD,cAAc,CAACoB,QAAQ,EAAE,WAAW,CAAC;EAC3E;EAEA4D,cAAcA,CAAC9B,QAAkB;IAC/B,IAAI,CAAC6B,oBAAoB,CAAC7B,QAAQ,EAAElD,cAAc,CAACqB,MAAM,EAAE,SAAS,CAAC;EACvE;EAEA0D,oBAAoBA,CAAC7B,QAAkB,EAAE+B,SAAyB,EAAEC,MAAc;IAChF,IAAIP,OAAO,CAAC,4BAA4BO,MAAM,iBAAiBhC,QAAQ,CAACE,KAAK,KAAK,CAAC,EAAE;MACnF,IAAI,CAACzC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACP,eAAe,CAAC+E,oBAAoB,CAACjC,QAAQ,CAAC0B,EAAE,EAAEK,SAAS,CAAC,CAACvC,SAAS,CAAC;QAC1EC,IAAI,EAAGyC,eAAe,IAAI;UACxB,MAAMC,KAAK,GAAG,IAAI,CAAC7E,SAAS,CAAC8E,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAK1B,QAAQ,CAAC0B,EAAE,CAAC;UACjE,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC7E,SAAS,CAAC6E,KAAK,CAAC,GAAGD,eAAe;YACvC,IAAI,CAACpC,YAAY,EAAE;;UAErB,IAAI,CAACyB,WAAW,CAAC,YAAYS,MAAM,KAAK,WAAW,GAAG,UAAU,GAAG,QAAQ,cAAc,CAAC;UAC1F,IAAI,CAACvE,OAAO,GAAG,KAAK;UAEpB;QACF,CAAC;;QACDkC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;UAC1D,IAAI,CAACnC,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA6E,cAAcA,CAACtC,QAAkB;IAC/B,IAAIyB,OAAO,CAAC,mDAAmDzB,QAAQ,CAACE,KAAK,KAAK,CAAC,EAAE;MACnF,IAAI,CAACzC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACP,eAAe,CAACoF,cAAc,CAACtC,QAAQ,CAAC0B,EAAE,CAAC,CAAClC,SAAS,CAAC;QACzDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACnC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACyC,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAK1B,QAAQ,CAAC0B,EAAE,CAAC;UACjE,IAAI,CAAC5B,YAAY,EAAE;UACnB,IAAI,CAACyB,WAAW,CAAC,+BAA+B,CAAC;UACjD,IAAI,CAAC9D,OAAO,GAAG,KAAK;QACtB,CAAC;QACDkC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,2CAA2C,CAAC;UAC3D,IAAI,CAACnC,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA8E,cAAcA,CAAChC,MAAsB;IACnC,MAAMiC,MAAM,GAAG,IAAI,CAAC1E,aAAa,CAAC2E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3E,KAAK,KAAKwC,MAAM,CAAC;IACnE,OAAOiC,MAAM,GAAGA,MAAM,CAACvE,KAAK,GAAG,SAAS;EAC1C;EAEA0E,cAAcA,CAACpC,MAAsB;IACnC,QAAQA,MAAM;MACZ,KAAKzD,cAAc,CAACkB,SAAS;QAC3B,OAAO,gBAAgB;MACzB,KAAKlB,cAAc,CAACoB,QAAQ;QAC1B,OAAO,iBAAiB;MAC1B,KAAKpB,cAAc,CAACqB,MAAM;QACxB,OAAO,eAAe;MACxB;QACE,OAAO,EAAE;;EAEf;EAEAyE,mBAAmBA,CAACC,WAAgC;IAClD,MAAML,MAAM,GAAG,IAAI,CAACpE,kBAAkB,CAACqE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3E,KAAK,KAAK8E,WAAW,CAAC;IAC7E,OAAOL,MAAM,GAAGA,MAAM,CAACvE,KAAK,GAAG,SAAS;EAC1C;EAEA6E,mBAAmBA,CAACrC,cAAsB;IACxC,MAAMY,IAAI,GAAG,IAAI,CAAC7D,cAAc,CAACiF,IAAI,CAACd,CAAC,IAAIA,CAAC,CAACD,EAAE,KAAKjB,cAAc,CAAC;IACnE,OAAOY,IAAI,GAAGA,IAAI,CAAClC,GAAG,GAAG,cAAc;EACzC;EAEA4D,UAAUA,CAAC/C,QAAkB;IAC3B,OAAOA,QAAQ,CAACO,MAAM,KAAKzD,cAAc,CAACkB,SAAS;EACrD;EAEAgF,SAASA,CAAChD,QAAkB;IAC1B,OAAOA,QAAQ,CAACO,MAAM,KAAKzD,cAAc,CAACkB,SAAS;EACrD;EAEQkD,oBAAoBA,CAAC+B,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACO,GAAG,CAACF,GAAG,CAAC;MAClCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQlC,WAAWA,CAACmC,OAAe;IACjC,IAAI,CAACrG,QAAQ,CAACuB,IAAI,CAAC8E,OAAO,EAAE,QAAQ,EAAE;MACpC7E,QAAQ,EAAE,IAAI;MACd8E,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQ/D,SAASA,CAAC8D,OAAe;IAC/B,IAAI,CAACrG,QAAQ,CAACuB,IAAI,CAAC8E,OAAO,EAAE,QAAQ,EAAE;MACpC7E,QAAQ,EAAE,IAAI;MACd8E,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAIxE,GAAGA,CAAA;IAAK,OAAO,IAAI,CAACF,gBAAgB,CAACuE,GAAG,CAAC,KAAK,CAAC;EAAE;EACrD,IAAIlE,WAAWA,CAAA;IAAK,OAAO,IAAI,CAACL,gBAAgB,CAACuE,GAAG,CAAC,aAAa,CAAC;EAAE;;;uBAvQ1DxG,uBAAuB,EAAA4G,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBrH,uBAAuB;MAAAsH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpCf,EAAA,CAAAiB,cAAA,QAAG;UAAAjB,EAAA,CAAAkB,MAAA,6BAAsB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}