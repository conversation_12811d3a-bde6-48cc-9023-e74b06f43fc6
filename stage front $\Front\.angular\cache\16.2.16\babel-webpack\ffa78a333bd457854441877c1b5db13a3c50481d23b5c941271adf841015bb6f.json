{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DocumentService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5251/api/documents';\n    this.TYPE_DOCUMENT_URL = 'http://localhost:5251/api/type-documents';\n  }\n  // Documents\n  getDocuments() {\n    return this.http.get(this.API_URL);\n  }\n  getDocument(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  createDocument(document) {\n    return this.http.post(this.API_URL, document);\n  }\n  updateDocument(id, document) {\n    return this.http.put(`${this.API_URL}/${id}`, document);\n  }\n  deleteDocument(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  approveDocument(id) {\n    return this.http.patch(`${this.API_URL}/${id}/approve`, {});\n  }\n  rejectDocument(id, commentaire) {\n    return this.http.patch(`${this.API_URL}/${id}/reject`, {\n      commentaire\n    });\n  }\n  getDocumentsByStatut(statut) {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  getDocumentsByType(typeId) {\n    const params = new HttpParams().set('typeId', typeId);\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  generatePdf(id) {\n    return this.http.get(`${this.API_URL}/${id}/pdf`, {\n      responseType: 'blob'\n    });\n  }\n  // Méthodes spécifiques pour les utilisateurs\n  getMyDocuments() {\n    return this.http.get(`${this.API_URL}/my-documents`);\n  }\n  updateDocumentStatus(id, statut) {\n    return this.http.patch(`${this.API_URL}/${id}/status`, {\n      statut\n    });\n  }\n  // Types de documents\n  getTypesDocuments() {\n    return this.http.get(`${this.API_URL}/types`);\n  }\n  getTypesDocument() {\n    return this.http.get(this.TYPE_DOCUMENT_URL);\n  }\n  getTypeDocument(id) {\n    return this.http.get(`${this.TYPE_DOCUMENT_URL}/${id}`);\n  }\n  createTypeDocument(typeDocument) {\n    return this.http.post(this.TYPE_DOCUMENT_URL, typeDocument);\n  }\n  updateTypeDocument(id, typeDocument) {\n    return this.http.put(`${this.TYPE_DOCUMENT_URL}/${id}`, typeDocument);\n  }\n  deleteTypeDocument(id) {\n    return this.http.delete(`${this.TYPE_DOCUMENT_URL}/${id}`);\n  }\n  static {\n    this.ɵfac = function DocumentService_Factory(t) {\n      return new (t || DocumentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DocumentService,\n      factory: DocumentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "DocumentService", "constructor", "http", "API_URL", "TYPE_DOCUMENT_URL", "getDocuments", "get", "getDocument", "id", "createDocument", "document", "post", "updateDocument", "put", "deleteDocument", "delete", "approveDocument", "patch", "rejectDocument", "commentaire", "getDocumentsByStatut", "statut", "params", "set", "toString", "getDocumentsByType", "typeId", "generatePdf", "responseType", "getMyDocuments", "updateDocumentStatus", "getTypesDocuments", "getTypesDocument", "getTypeDocument", "createTypeDocument", "typeDocument", "updateTypeDocument", "deleteTypeDocument", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\document.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Document, TypeDocument } from 'src/app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DocumentService {\n  private readonly API_URL = 'http://localhost:5251/api/documents';\n  private readonly TYPE_DOCUMENT_URL = 'http://localhost:5251/api/type-documents';\n\n  constructor(private http: HttpClient) {}\n\n  // Documents\n  getDocuments(): Observable<Document[]> {\n    return this.http.get<Document[]>(this.API_URL);\n  }\n\n  getDocument(id: string): Observable<Document> {\n    return this.http.get<Document>(`${this.API_URL}/${id}`);\n  }\n\n  createDocument(document: Partial<Document>): Observable<Document> {\n    return this.http.post<Document>(this.API_URL, document);\n  }\n\n  updateDocument(id: string, document: Partial<Document>): Observable<Document> {\n    return this.http.put<Document>(`${this.API_URL}/${id}`, document);\n  }\n\n  deleteDocument(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  approveDocument(id: string): Observable<Document> {\n    return this.http.patch<Document>(`${this.API_URL}/${id}/approve`, {});\n  }\n\n  rejectDocument(id: string, commentaire?: string): Observable<Document> {\n    return this.http.patch<Document>(`${this.API_URL}/${id}/reject`, { commentaire });\n  }\n\n  getDocumentsByStatut(statut: number): Observable<Document[]> {\n    const params = new HttpParams().set('statut', statut.toString());\n    return this.http.get<Document[]>(this.API_URL, { params });\n  }\n\n  getDocumentsByType(typeId: string): Observable<Document[]> {\n    const params = new HttpParams().set('typeId', typeId);\n    return this.http.get<Document[]>(this.API_URL, { params });\n  }\n\n  generatePdf(id: string): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/${id}/pdf`, { \n      responseType: 'blob' \n    });\n  }\n\n  // Méthodes spécifiques pour les utilisateurs\n  getMyDocuments(): Observable<Document[]> {\n    return this.http.get<Document[]>(`${this.API_URL}/my-documents`);\n  }\n\n  updateDocumentStatus(id: string, statut: number): Observable<Document> {\n    return this.http.patch<Document>(`${this.API_URL}/${id}/status`, { statut });\n  }\n\n  // Types de documents\n  getTypesDocuments(): Observable<TypeDocument[]> {\n    return this.http.get<TypeDocument[]>(`${this.API_URL}/types`);\n  }\n\n  getTypesDocument(): Observable<TypeDocument[]> {\n    return this.http.get<TypeDocument[]>(this.TYPE_DOCUMENT_URL);\n  }\n\n  getTypeDocument(id: string): Observable<TypeDocument> {\n    return this.http.get<TypeDocument>(`${this.TYPE_DOCUMENT_URL}/${id}`);\n  }\n\n  createTypeDocument(typeDocument: Partial<TypeDocument>): Observable<TypeDocument> {\n    return this.http.post<TypeDocument>(this.TYPE_DOCUMENT_URL, typeDocument);\n  }\n\n  updateTypeDocument(id: string, typeDocument: Partial<TypeDocument>): Observable<TypeDocument> {\n    return this.http.put<TypeDocument>(`${this.TYPE_DOCUMENT_URL}/${id}`, typeDocument);\n  }\n\n  deleteTypeDocument(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.TYPE_DOCUMENT_URL}/${id}`);\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAO7D,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHP,KAAAC,OAAO,GAAG,qCAAqC;IAC/C,KAAAC,iBAAiB,GAAG,0CAA0C;EAExC;EAEvC;EACAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAa,IAAI,CAACH,OAAO,CAAC;EAChD;EAEAI,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAW,GAAG,IAAI,CAACH,OAAO,IAAIK,EAAE,EAAE,CAAC;EACzD;EAEAC,cAAcA,CAACC,QAA2B;IACxC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAW,IAAI,CAACR,OAAO,EAAEO,QAAQ,CAAC;EACzD;EAEAE,cAAcA,CAACJ,EAAU,EAAEE,QAA2B;IACpD,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAW,GAAG,IAAI,CAACV,OAAO,IAAIK,EAAE,EAAE,EAAEE,QAAQ,CAAC;EACnE;EAEAI,cAAcA,CAACN,EAAU;IACvB,OAAO,IAAI,CAACN,IAAI,CAACa,MAAM,CAAO,GAAG,IAAI,CAACZ,OAAO,IAAIK,EAAE,EAAE,CAAC;EACxD;EAEAQ,eAAeA,CAACR,EAAU;IACxB,OAAO,IAAI,CAACN,IAAI,CAACe,KAAK,CAAW,GAAG,IAAI,CAACd,OAAO,IAAIK,EAAE,UAAU,EAAE,EAAE,CAAC;EACvE;EAEAU,cAAcA,CAACV,EAAU,EAAEW,WAAoB;IAC7C,OAAO,IAAI,CAACjB,IAAI,CAACe,KAAK,CAAW,GAAG,IAAI,CAACd,OAAO,IAAIK,EAAE,SAAS,EAAE;MAAEW;IAAW,CAAE,CAAC;EACnF;EAEAC,oBAAoBA,CAACC,MAAc;IACjC,MAAMC,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAACwB,GAAG,CAAC,QAAQ,EAAEF,MAAM,CAACG,QAAQ,EAAE,CAAC;IAChE,OAAO,IAAI,CAACtB,IAAI,CAACI,GAAG,CAAa,IAAI,CAACH,OAAO,EAAE;MAAEmB;IAAM,CAAE,CAAC;EAC5D;EAEAG,kBAAkBA,CAACC,MAAc;IAC/B,MAAMJ,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAACwB,GAAG,CAAC,QAAQ,EAAEG,MAAM,CAAC;IACrD,OAAO,IAAI,CAACxB,IAAI,CAACI,GAAG,CAAa,IAAI,CAACH,OAAO,EAAE;MAAEmB;IAAM,CAAE,CAAC;EAC5D;EAEAK,WAAWA,CAACnB,EAAU;IACpB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAC,GAAG,IAAI,CAACH,OAAO,IAAIK,EAAE,MAAM,EAAE;MAChDoB,YAAY,EAAE;KACf,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3B,IAAI,CAACI,GAAG,CAAa,GAAG,IAAI,CAACH,OAAO,eAAe,CAAC;EAClE;EAEA2B,oBAAoBA,CAACtB,EAAU,EAAEa,MAAc;IAC7C,OAAO,IAAI,CAACnB,IAAI,CAACe,KAAK,CAAW,GAAG,IAAI,CAACd,OAAO,IAAIK,EAAE,SAAS,EAAE;MAAEa;IAAM,CAAE,CAAC;EAC9E;EAEA;EACAU,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC7B,IAAI,CAACI,GAAG,CAAiB,GAAG,IAAI,CAACH,OAAO,QAAQ,CAAC;EAC/D;EAEA6B,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC9B,IAAI,CAACI,GAAG,CAAiB,IAAI,CAACF,iBAAiB,CAAC;EAC9D;EAEA6B,eAAeA,CAACzB,EAAU;IACxB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAe,GAAG,IAAI,CAACF,iBAAiB,IAAII,EAAE,EAAE,CAAC;EACvE;EAEA0B,kBAAkBA,CAACC,YAAmC;IACpD,OAAO,IAAI,CAACjC,IAAI,CAACS,IAAI,CAAe,IAAI,CAACP,iBAAiB,EAAE+B,YAAY,CAAC;EAC3E;EAEAC,kBAAkBA,CAAC5B,EAAU,EAAE2B,YAAmC;IAChE,OAAO,IAAI,CAACjC,IAAI,CAACW,GAAG,CAAe,GAAG,IAAI,CAACT,iBAAiB,IAAII,EAAE,EAAE,EAAE2B,YAAY,CAAC;EACrF;EAEAE,kBAAkBA,CAAC7B,EAAU;IAC3B,OAAO,IAAI,CAACN,IAAI,CAACa,MAAM,CAAO,GAAG,IAAI,CAACX,iBAAiB,IAAII,EAAE,EAAE,CAAC;EAClE;;;uBAnFWR,eAAe,EAAAsC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfzC,eAAe;MAAA0C,OAAA,EAAf1C,eAAe,CAAA2C,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}