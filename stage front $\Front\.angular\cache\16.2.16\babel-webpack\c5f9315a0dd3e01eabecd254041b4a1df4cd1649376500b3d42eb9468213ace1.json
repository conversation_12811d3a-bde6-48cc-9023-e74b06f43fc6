{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let FactureAchatService = /*#__PURE__*/(() => {\n  class FactureAchatService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = '/api/factures-achat';\n    }\n    getFacturesAchat() {\n      return this.http.get(this.API_URL);\n    }\n    getFactureAchat(id) {\n      return this.http.get(`${this.API_URL}/${id}`);\n    }\n    createFactureAchat(factureData) {\n      const formData = new FormData();\n      // Ajouter les données de la facture\n      formData.append('numero', factureData.numero);\n      formData.append('date', factureData.date.toISOString());\n      formData.append('montant', factureData.montant.toString());\n      formData.append('fournisseurId', factureData.fournisseurId);\n      if (factureData.dateEcheance) {\n        formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n      }\n      if (factureData.notesInternes) {\n        formData.append('notesInternes', factureData.notesInternes);\n      }\n      if (factureData.statut !== undefined) {\n        formData.append('statut', factureData.statut.toString());\n      }\n      // Ajouter le fichier si présent\n      if (factureData.fichier) {\n        formData.append('fichier', factureData.fichier);\n      }\n      return this.http.post(this.API_URL, formData);\n    }\n    updateFactureAchat(id, factureData) {\n      const formData = new FormData();\n      formData.append('numero', factureData.numero);\n      formData.append('date', factureData.date.toISOString());\n      formData.append('montant', factureData.montant.toString());\n      formData.append('fournisseurId', factureData.fournisseurId);\n      formData.append('statut', factureData.statut.toString());\n      if (factureData.dateEcheance) {\n        formData.append('dateEcheance', factureData.dateEcheance.toISOString());\n      }\n      if (factureData.notesInternes) {\n        formData.append('notesInternes', factureData.notesInternes);\n      }\n      if (factureData.fichier) {\n        formData.append('fichier', factureData.fichier);\n      }\n      return this.http.put(`${this.API_URL}/${id}`, formData);\n    }\n    deleteFactureAchat(id) {\n      return this.http.delete(`${this.API_URL}/${id}`);\n    }\n    downloadFile(id) {\n      return this.http.get(`${this.API_URL}/${id}/download`, {\n        responseType: 'blob'\n      });\n    }\n    uploadFile(id, file) {\n      const formData = new FormData();\n      formData.append('fichier', file);\n      return this.http.post(`${this.API_URL}/${id}/upload`, formData);\n    }\n    getFacturesByFournisseur(fournisseurId) {\n      const params = new HttpParams().set('fournisseurId', fournisseurId);\n      return this.http.get(this.API_URL, {\n        params\n      });\n    }\n    getFacturesByStatut(statut) {\n      const params = new HttpParams().set('statut', statut.toString());\n      return this.http.get(this.API_URL, {\n        params\n      });\n    }\n    searchFactures(searchTerm) {\n      const params = new HttpParams().set('search', searchTerm);\n      return this.http.get(`${this.API_URL}/search`, {\n        params\n      });\n    }\n    // Méthodes pour l'upload de fichiers\n    createFactureAchatWithFile(formData) {\n      return this.http.post(`${this.API_URL}/with-file`, formData);\n    }\n    updateFactureAchatWithFile(id, formData) {\n      return this.http.put(`${this.API_URL}/${id}/with-file`, formData);\n    }\n    deleteFile(factureId) {\n      return this.http.delete(`${this.API_URL}/${factureId}/file`);\n    }\n    static {\n      this.ɵfac = function FactureAchatService_Factory(t) {\n        return new (t || FactureAchatService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FactureAchatService,\n        factory: FactureAchatService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FactureAchatService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}