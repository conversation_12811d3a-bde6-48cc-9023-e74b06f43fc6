{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RetenueSourceService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = '/api/RetenueSource';\n  }\n  getRetenuesSources() {\n    return this.http.get(this.API_URL);\n  }\n  getRetenuesSource() {\n    return this.http.get(this.API_URL);\n  }\n  getRetenueSource(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  uploadRetenue(formData) {\n    return this.http.post(this.API_URL, formData);\n  }\n  uploadRetenueSource(file, dateScan) {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    if (dateScan) {\n      formData.append('dateScan', dateScan.toISOString());\n    }\n    return this.http.post(this.API_URL, formData);\n  }\n  deleteRetenue(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  downloadFile(id) {\n    return this.http.get(`${this.API_URL}/${id}/download`, {\n      responseType: 'blob'\n    });\n  }\n  scanDocument(file) {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    return this.http.post(`${this.API_URL}/scan`, formData);\n  }\n  getRetenuesByDateRange(dateDebut, dateFin) {\n    const params = new HttpParams().set('dateDebut', dateDebut.toISOString()).set('dateFin', dateFin.toISOString());\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  getRetenuesByUtilisateur(utilisateurId) {\n    const params = new HttpParams().set('utilisateurId', utilisateurId);\n    return this.http.get(this.API_URL, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function RetenueSourceService_Factory(t) {\n      return new (t || RetenueSourceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RetenueSourceService,\n      factory: RetenueSourceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "RetenueSourceService", "constructor", "http", "API_URL", "getRetenuesSources", "get", "getRetenuesSource", "getRetenueSource", "id", "uploadRetenue", "formData", "post", "uploadRetenueSource", "file", "dateScan", "FormData", "append", "toISOString", "deleteRetenue", "delete", "downloadFile", "responseType", "scanDocument", "getRetenuesByDateRange", "dateDebut", "dateFin", "params", "set", "getRetenuesByUtilisateur", "utilisateurId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\retenue-source.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { RetenueSource } from 'src/app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RetenueSourceService {\n  private readonly API_URL = '/api/RetenueSource';\n\n  constructor(private http: HttpClient) {}\n\n  getRetenuesSources(): Observable<RetenueSource[]> {\n    return this.http.get<RetenueSource[]>(this.API_URL);\n  }\n\n  getRetenuesSource(): Observable<RetenueSource[]> {\n    return this.http.get<RetenueSource[]>(this.API_URL);\n  }\n\n  getRetenueSource(id: string): Observable<RetenueSource> {\n    return this.http.get<RetenueSource>(`${this.API_URL}/${id}`);\n  }\n\n  uploadRetenue(formData: FormData): Observable<RetenueSource> {\n    return this.http.post<RetenueSource>(this.API_URL, formData);\n  }\n\n  uploadRetenueSource(file: File, dateScan?: Date): Observable<RetenueSource> {\n    const formData = new FormData();\n    formData.append('fichier', file);\n\n    if (dateScan) {\n      formData.append('dateScan', dateScan.toISOString());\n    }\n\n    return this.http.post<RetenueSource>(this.API_URL, formData);\n  }\n\n  deleteRetenue(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  downloadFile(id: string): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/${id}/download`, { \n      responseType: 'blob' \n    });\n  }\n\n  scanDocument(file: File): Observable<any> {\n    const formData = new FormData();\n    formData.append('fichier', file);\n    \n    return this.http.post(`${this.API_URL}/scan`, formData);\n  }\n\n  getRetenuesByDateRange(dateDebut: Date, dateFin: Date): Observable<RetenueSource[]> {\n    const params = new HttpParams()\n      .set('dateDebut', dateDebut.toISOString())\n      .set('dateFin', dateFin.toISOString());\n    \n    return this.http.get<RetenueSource[]>(this.API_URL, { params });\n  }\n\n  getRetenuesByUtilisateur(utilisateurId: string): Observable<RetenueSource[]> {\n    const params = new HttpParams().set('utilisateurId', utilisateurId);\n    return this.http.get<RetenueSource[]>(this.API_URL, { params });\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;;;AAO7D,OAAM,MAAOC,oBAAoB;EAG/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,oBAAoB;EAER;EAEvCC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,CAAC;EACrD;EAEAG,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACJ,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,CAAC;EACrD;EAEAI,gBAAgBA,CAACC,EAAU;IACzB,OAAO,IAAI,CAACN,IAAI,CAACG,GAAG,CAAgB,GAAG,IAAI,CAACF,OAAO,IAAIK,EAAE,EAAE,CAAC;EAC9D;EAEAC,aAAaA,CAACC,QAAkB;IAC9B,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAgB,IAAI,CAACR,OAAO,EAAEO,QAAQ,CAAC;EAC9D;EAEAE,mBAAmBA,CAACC,IAAU,EAAEC,QAAe;IAC7C,MAAMJ,QAAQ,GAAG,IAAIK,QAAQ,EAAE;IAC/BL,QAAQ,CAACM,MAAM,CAAC,SAAS,EAAEH,IAAI,CAAC;IAEhC,IAAIC,QAAQ,EAAE;MACZJ,QAAQ,CAACM,MAAM,CAAC,UAAU,EAAEF,QAAQ,CAACG,WAAW,EAAE,CAAC;;IAGrD,OAAO,IAAI,CAACf,IAAI,CAACS,IAAI,CAAgB,IAAI,CAACR,OAAO,EAAEO,QAAQ,CAAC;EAC9D;EAEAQ,aAAaA,CAACV,EAAU;IACtB,OAAO,IAAI,CAACN,IAAI,CAACiB,MAAM,CAAO,GAAG,IAAI,CAAChB,OAAO,IAAIK,EAAE,EAAE,CAAC;EACxD;EAEAY,YAAYA,CAACZ,EAAU;IACrB,OAAO,IAAI,CAACN,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,IAAIK,EAAE,WAAW,EAAE;MACrDa,YAAY,EAAE;KACf,CAAC;EACJ;EAEAC,YAAYA,CAACT,IAAU;IACrB,MAAMH,QAAQ,GAAG,IAAIK,QAAQ,EAAE;IAC/BL,QAAQ,CAACM,MAAM,CAAC,SAAS,EAAEH,IAAI,CAAC;IAEhC,OAAO,IAAI,CAACX,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,OAAO,EAAEO,QAAQ,CAAC;EACzD;EAEAa,sBAAsBA,CAACC,SAAe,EAAEC,OAAa;IACnD,MAAMC,MAAM,GAAG,IAAI3B,UAAU,EAAE,CAC5B4B,GAAG,CAAC,WAAW,EAAEH,SAAS,CAACP,WAAW,EAAE,CAAC,CACzCU,GAAG,CAAC,SAAS,EAAEF,OAAO,CAACR,WAAW,EAAE,CAAC;IAExC,OAAO,IAAI,CAACf,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,EAAE;MAAEuB;IAAM,CAAE,CAAC;EACjE;EAEAE,wBAAwBA,CAACC,aAAqB;IAC5C,MAAMH,MAAM,GAAG,IAAI3B,UAAU,EAAE,CAAC4B,GAAG,CAAC,eAAe,EAAEE,aAAa,CAAC;IACnE,OAAO,IAAI,CAAC3B,IAAI,CAACG,GAAG,CAAkB,IAAI,CAACF,OAAO,EAAE;MAAEuB;IAAM,CAAE,CAAC;EACjE;;;uBA5DW1B,oBAAoB,EAAA8B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBjC,oBAAoB;MAAAkC,OAAA,EAApBlC,oBAAoB,CAAAmC,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}