{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nexport class UsersListComponent {\n  constructor(userService, authService, formBuilder, snackBar) {\n    this.userService = userService;\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.users = []; // Utilisation de any car le service retourne des DTOs\n    this.filteredUsers = [];\n    this.loading = false;\n    this.searchTerm = '';\n    this.selectedRole = null;\n    this.isEditing = false;\n    this.editingUserId = null;\n    this.showForm = false;\n    // Options de rôles\n    this.roleOptions = [{\n      value: 'Admin',\n      label: 'Administrateur'\n    }, {\n      value: 'User',\n      label: 'Utilisateur'\n    }];\n    // Colonnes à afficher dans le tableau\n    this.displayedColumns = ['nom', 'email', 'userName', 'role', 'dateCreation', 'actif', 'actions'];\n  }\n  ngOnInit() {\n    // Vérifier les permissions - seuls les admins peuvent gérer les utilisateurs\n    if (!this.authService.canManageUsers()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', {\n        duration: 3000\n      });\n      return;\n    }\n    this.initializeForm();\n    this.loadUsers();\n  }\n  initializeForm() {\n    this.userForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      userName: ['', [Validators.required, Validators.minLength(3)]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]],\n      role: ['User', [Validators.required]],\n      actif: [true]\n    });\n  }\n  loadUsers() {\n    this.loading = true;\n    this.userService.getUtilisateurs().subscribe({\n      next: users => {\n        this.users = users;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des utilisateurs');\n        this.loading = false;\n      }\n    });\n  }\n  applyFilters() {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = !this.searchTerm || user.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.userName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesRole = this.selectedRole === null || user.role === this.selectedRole;\n      return matchesSearch && matchesRole;\n    });\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  onRoleFilterChange() {\n    this.applyFilters();\n  }\n  showAddForm() {\n    this.isEditing = false;\n    this.editingUserId = null;\n    this.userForm.reset();\n    this.userForm.patchValue({\n      role: 'User',\n      actif: true\n    });\n    // Le mot de passe est requis pour la création\n    this.userForm.get('motDePasse')?.setValidators([Validators.required, Validators.minLength(6)]);\n    this.userForm.get('motDePasse')?.updateValueAndValidity();\n    this.showForm = true;\n  }\n  editUser(user) {\n    this.isEditing = true;\n    this.editingUserId = user.id;\n    this.userForm.patchValue({\n      nom: user.nom,\n      email: user.email,\n      userName: user.userName,\n      role: user.role,\n      actif: user.actif\n    });\n    // Le mot de passe n'est pas requis pour la modification\n    this.userForm.get('motDePasse')?.clearValidators();\n    this.userForm.get('motDePasse')?.updateValueAndValidity();\n    this.showForm = true;\n  }\n  cancelForm() {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingUserId = null;\n    this.userForm.reset();\n  }\n  onSubmit() {\n    if (this.userForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    const userData = this.userForm.value;\n    // Ne pas envoyer le mot de passe vide lors de la modification\n    if (this.isEditing && !userData.motDePasse) {\n      delete userData.motDePasse;\n    }\n    if (this.isEditing && this.editingUserId) {\n      this.updateUser(this.editingUserId, userData);\n    } else {\n      this.createUser(userData);\n    }\n  }\n  createUser(userData) {\n    this.loading = true;\n    this.userService.createUtilisateur(userData).subscribe({\n      next: user => {\n        this.users.push(user);\n        this.applyFilters();\n        this.showSuccess('Utilisateur créé avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors de la création de l\\'utilisateur');\n        this.loading = false;\n      }\n    });\n  }\n  updateUser(id, userData) {\n    this.loading = true;\n    this.userService.updateUtilisateur(id, userData).subscribe({\n      next: () => {\n        // Recharger la liste après modification\n        this.loadUsers();\n        this.showSuccess('Utilisateur modifié avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors de la modification de l\\'utilisateur');\n        this.loading = false;\n      }\n    });\n  }\n  deleteUser(user) {\n    // Empêcher la suppression de son propre compte\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser && currentUser.id === user.id) {\n      this.showError('Vous ne pouvez pas supprimer votre propre compte');\n      return;\n    }\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur \"${user.nom}\" ?`)) {\n      this.loading = true;\n      this.userService.deleteUtilisateur(user.id).subscribe({\n        next: () => {\n          this.users = this.users.filter(u => u.id !== user.id);\n          this.applyFilters();\n          this.showSuccess('Utilisateur supprimé avec succès');\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la suppression de l\\'utilisateur');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  toggleUserStatus(user) {\n    const newStatus = !user.actif;\n    const action = newStatus ? 'activer' : 'désactiver';\n    if (confirm(`Êtes-vous sûr de vouloir ${action} l'utilisateur \"${user.nom}\" ?`)) {\n      this.loading = true;\n      this.userService.updateUtilisateur(user.id, {\n        actif: newStatus\n      }).subscribe({\n        next: () => {\n          user.actif = newStatus;\n          this.showSuccess(`Utilisateur ${newStatus ? 'activé' : 'désactivé'} avec succès`);\n          this.loading = false;\n        },\n        error: error => {\n          this.showError(`Erreur lors de la modification du statut`);\n          this.loading = false;\n        }\n      });\n    }\n  }\n  getRoleLabel(role) {\n    const option = this.roleOptions.find(opt => opt.value === role);\n    return option ? option.label : role;\n  }\n  getRoleClass(role) {\n    switch (role) {\n      case 'Admin':\n        return 'role-admin';\n      case 'User':\n        return 'role-user';\n      default:\n        return '';\n    }\n  }\n  canDeleteUser(user) {\n    const currentUser = this.authService.getCurrentUser();\n    return !!(currentUser && currentUser.id !== user.id);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.userForm.controls).forEach(key => {\n      const control = this.userForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() {\n    return this.userForm.get('nom');\n  }\n  get email() {\n    return this.userForm.get('email');\n  }\n  get userName() {\n    return this.userForm.get('userName');\n  }\n  get motDePasse() {\n    return this.userForm.get('motDePasse');\n  }\n  get role() {\n    return this.userForm.get('role');\n  }\n  get actif() {\n    return this.userForm.get('actif');\n  }\n  static {\n    this.ɵfac = function UsersListComponent_Factory(t) {\n      return new (t || UsersListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersListComponent,\n      selectors: [[\"app-users-list\"]],\n      decls: 2,\n      vars: 0,\n      template: function UsersListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"users-list works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "UsersListComponent", "constructor", "userService", "authService", "formBuilder", "snackBar", "users", "filteredUsers", "loading", "searchTerm", "selectedR<PERSON>", "isEditing", "editingUserId", "showForm", "roleOptions", "value", "label", "displayedColumns", "ngOnInit", "canManageUsers", "open", "duration", "initializeForm", "loadUsers", "userForm", "group", "nom", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "userName", "motDePasse", "role", "actif", "getUtilisateurs", "subscribe", "next", "applyFilters", "error", "showError", "filter", "user", "matchesSearch", "toLowerCase", "includes", "matchesRole", "onSearchChange", "onRoleFilterChange", "showAddForm", "reset", "patchValue", "get", "setValidators", "updateValueAndValidity", "editUser", "id", "clearValidators", "cancelForm", "onSubmit", "invalid", "markFormGroupTouched", "userData", "updateUser", "createUser", "createUtilisateur", "push", "showSuccess", "updateUtilisateur", "deleteUser", "currentUser", "getCurrentUser", "confirm", "deleteUtilisateur", "u", "toggleUserStatus", "newStatus", "action", "getRoleLabel", "option", "find", "opt", "getRoleClass", "canDeleteUser", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "panelClass", "i0", "ɵɵdirectiveInject", "i1", "UserService", "i2", "AuthService", "i3", "FormBuilder", "i4", "MatSnackBar", "selectors", "decls", "vars", "template", "UsersListComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\users-list\\users-list.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\users-list\\users-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { UserService } from '../../services/user.service';\nimport { AuthService } from '../../services/auth.service';\n\n\n@Component({\n  selector: 'app-users-list',\n  templateUrl: './users-list.component.html',\n  styleUrls: ['./users-list.component.css']\n})\nexport class UsersListComponent implements OnInit {\n  users: any[] = []; // Utilisation de any car le service retourne des DTOs\n  filteredUsers: any[] = [];\n  loading = false;\n  searchTerm = '';\n  selectedRole: string | null = null;\n\n  // Formulaire pour ajouter/modifier un utilisateur\n  userForm!: FormGroup;\n  isEditing = false;\n  editingUserId: string | null = null;\n  showForm = false;\n\n  // Options de rôles\n  roleOptions = [\n    { value: 'Admin', label: 'Administrateur' },\n    { value: 'User', label: 'Utilisateur' }\n  ];\n\n  // Colonnes à afficher dans le tableau\n  displayedColumns: string[] = ['nom', 'email', 'userName', 'role', 'dateCreation', 'actif', 'actions'];\n\n  constructor(\n    private userService: UserService,\n    public authService: AuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Vérifier les permissions - seuls les admins peuvent gérer les utilisateurs\n    if (!this.authService.canManageUsers()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.initializeForm();\n    this.loadUsers();\n  }\n\n  initializeForm(): void {\n    this.userForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      userName: ['', [Validators.required, Validators.minLength(3)]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]],\n      role: ['User', [Validators.required]],\n      actif: [true]\n    });\n  }\n\n  loadUsers(): void {\n    this.loading = true;\n    this.userService.getUtilisateurs().subscribe({\n      next: (users) => {\n        this.users = users;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: (error: any) => {\n        this.showError('Erreur lors du chargement des utilisateurs');\n        this.loading = false;\n      }\n    });\n  }\n\n  applyFilters(): void {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = !this.searchTerm ||\n        user.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        user.userName.toLowerCase().includes(this.searchTerm.toLowerCase());\n\n      const matchesRole = this.selectedRole === null || user.role === this.selectedRole;\n\n      return matchesSearch && matchesRole;\n    });\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  onRoleFilterChange(): void {\n    this.applyFilters();\n  }\n\n  showAddForm(): void {\n    this.isEditing = false;\n    this.editingUserId = null;\n    this.userForm.reset();\n    this.userForm.patchValue({\n      role: 'User',\n      actif: true\n    });\n    // Le mot de passe est requis pour la création\n    this.userForm.get('motDePasse')?.setValidators([Validators.required, Validators.minLength(6)]);\n    this.userForm.get('motDePasse')?.updateValueAndValidity();\n    this.showForm = true;\n  }\n\n  editUser(user: any): void {\n    this.isEditing = true;\n    this.editingUserId = user.id;\n    this.userForm.patchValue({\n      nom: user.nom,\n      email: user.email,\n      userName: user.userName,\n      role: user.role,\n      actif: user.actif\n    });\n    // Le mot de passe n'est pas requis pour la modification\n    this.userForm.get('motDePasse')?.clearValidators();\n    this.userForm.get('motDePasse')?.updateValueAndValidity();\n    this.showForm = true;\n  }\n\n  cancelForm(): void {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingUserId = null;\n    this.userForm.reset();\n  }\n\n  onSubmit(): void {\n    if (this.userForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    const userData = this.userForm.value;\n\n    // Ne pas envoyer le mot de passe vide lors de la modification\n    if (this.isEditing && !userData.motDePasse) {\n      delete userData.motDePasse;\n    }\n\n    if (this.isEditing && this.editingUserId) {\n      this.updateUser(this.editingUserId, userData);\n    } else {\n      this.createUser(userData);\n    }\n  }\n\n  createUser(userData: any): void {\n    this.loading = true;\n    this.userService.createUtilisateur(userData).subscribe({\n      next: (user) => {\n        this.users.push(user);\n        this.applyFilters();\n        this.showSuccess('Utilisateur créé avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors de la création de l\\'utilisateur');\n        this.loading = false;\n      }\n    });\n  }\n\n  updateUser(id: string, userData: any): void {\n    this.loading = true;\n    this.userService.updateUtilisateur(id, userData).subscribe({\n      next: () => {\n        // Recharger la liste après modification\n        this.loadUsers();\n        this.showSuccess('Utilisateur modifié avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors de la modification de l\\'utilisateur');\n        this.loading = false;\n      }\n    });\n  }\n\n  deleteUser(user: any): void {\n    // Empêcher la suppression de son propre compte\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser && currentUser.id === user.id) {\n      this.showError('Vous ne pouvez pas supprimer votre propre compte');\n      return;\n    }\n\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur \"${user.nom}\" ?`)) {\n      this.loading = true;\n      this.userService.deleteUtilisateur(user.id).subscribe({\n        next: () => {\n          this.users = this.users.filter(u => u.id !== user.id);\n          this.applyFilters();\n          this.showSuccess('Utilisateur supprimé avec succès');\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la suppression de l\\'utilisateur');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  toggleUserStatus(user: any): void {\n    const newStatus = !user.actif;\n    const action = newStatus ? 'activer' : 'désactiver';\n\n    if (confirm(`Êtes-vous sûr de vouloir ${action} l'utilisateur \"${user.nom}\" ?`)) {\n      this.loading = true;\n      this.userService.updateUtilisateur(user.id, { actif: newStatus }).subscribe({\n        next: () => {\n          user.actif = newStatus;\n          this.showSuccess(`Utilisateur ${newStatus ? 'activé' : 'désactivé'} avec succès`);\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError(`Erreur lors de la modification du statut`);\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  getRoleLabel(role: string): string {\n    const option = this.roleOptions.find(opt => opt.value === role);\n    return option ? option.label : role;\n  }\n\n  getRoleClass(role: string): string {\n    switch (role) {\n      case 'Admin':\n        return 'role-admin';\n      case 'User':\n        return 'role-user';\n      default:\n        return '';\n    }\n  }\n\n  canDeleteUser(user: any): boolean {\n    const currentUser = this.authService.getCurrentUser();\n    return !!(currentUser && currentUser.id !== user.id);\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.userForm.controls).forEach(key => {\n      const control = this.userForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() { return this.userForm.get('nom'); }\n  get email() { return this.userForm.get('email'); }\n  get userName() { return this.userForm.get('userName'); }\n  get motDePasse() { return this.userForm.get('motDePasse'); }\n  get role() { return this.userForm.get('role'); }\n  get actif() { return this.userForm.get('actif'); }\n}\n", "<p>users-list works!</p>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;AAWnE,OAAM,MAAOC,kBAAkB;EAsB7BC,YACUC,WAAwB,EACzBC,WAAwB,EACvBC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAzBlB,KAAAC,KAAK,GAAU,EAAE,CAAC,CAAC;IACnB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAkB,IAAI;IAIlC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAC,WAAW,GAAG,CACZ;MAAEC,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAa,CAAE,CACxC;IAED;IACA,KAAAC,gBAAgB,GAAa,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,SAAS,CAAC;EAOlG;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACf,WAAW,CAACgB,cAAc,EAAE,EAAE;MACtC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,kDAAkD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpG;;IAGF,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACrCC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC8B,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEI,IAAI,EAAE,CAAC,MAAM,EAAE,CAACjC,UAAU,CAAC4B,QAAQ,CAAC,CAAC;MACrCM,KAAK,EAAE,CAAC,IAAI;KACb,CAAC;EACJ;EAEAV,SAASA,CAAA;IACP,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,WAAW,CAACgC,eAAe,EAAE,CAACC,SAAS,CAAC;MAC3CC,IAAI,EAAG9B,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC+B,YAAY,EAAE;QACnB,IAAI,CAAC7B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8B,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACC,SAAS,CAAC,4CAA4C,CAAC;QAC5D,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA6B,YAAYA,CAAA;IACV,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACD,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAG;MAC5C,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACjC,UAAU,IACpCgC,IAAI,CAACf,GAAG,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnC,UAAU,CAACkC,WAAW,EAAE,CAAC,IAC9DF,IAAI,CAACZ,KAAK,CAACc,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnC,UAAU,CAACkC,WAAW,EAAE,CAAC,IAChEF,IAAI,CAACX,QAAQ,CAACa,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnC,UAAU,CAACkC,WAAW,EAAE,CAAC;MAErE,MAAME,WAAW,GAAG,IAAI,CAACnC,YAAY,KAAK,IAAI,IAAI+B,IAAI,CAACT,IAAI,KAAK,IAAI,CAACtB,YAAY;MAEjF,OAAOgC,aAAa,IAAIG,WAAW;IACrC,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACT,YAAY,EAAE;EACrB;EAEAU,kBAAkBA,CAAA;IAChB,IAAI,CAACV,YAAY,EAAE;EACrB;EAEAW,WAAWA,CAAA;IACT,IAAI,CAACrC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACY,QAAQ,CAACyB,KAAK,EAAE;IACrB,IAAI,CAACzB,QAAQ,CAAC0B,UAAU,CAAC;MACvBlB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,CAAC;IACF;IACA,IAAI,CAACT,QAAQ,CAAC2B,GAAG,CAAC,YAAY,CAAC,EAAEC,aAAa,CAAC,CAACrD,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACJ,QAAQ,CAAC2B,GAAG,CAAC,YAAY,CAAC,EAAEE,sBAAsB,EAAE;IACzD,IAAI,CAACxC,QAAQ,GAAG,IAAI;EACtB;EAEAyC,QAAQA,CAACb,IAAS;IAChB,IAAI,CAAC9B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG6B,IAAI,CAACc,EAAE;IAC5B,IAAI,CAAC/B,QAAQ,CAAC0B,UAAU,CAAC;MACvBxB,GAAG,EAAEe,IAAI,CAACf,GAAG;MACbG,KAAK,EAAEY,IAAI,CAACZ,KAAK;MACjBC,QAAQ,EAAEW,IAAI,CAACX,QAAQ;MACvBE,IAAI,EAAES,IAAI,CAACT,IAAI;MACfC,KAAK,EAAEQ,IAAI,CAACR;KACb,CAAC;IACF;IACA,IAAI,CAACT,QAAQ,CAAC2B,GAAG,CAAC,YAAY,CAAC,EAAEK,eAAe,EAAE;IAClD,IAAI,CAAChC,QAAQ,CAAC2B,GAAG,CAAC,YAAY,CAAC,EAAEE,sBAAsB,EAAE;IACzD,IAAI,CAACxC,QAAQ,GAAG,IAAI;EACtB;EAEA4C,UAAUA,CAAA;IACR,IAAI,CAAC5C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACY,QAAQ,CAACyB,KAAK,EAAE;EACvB;EAEAS,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClC,QAAQ,CAACmC,OAAO,EAAE;MACzB,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,MAAMC,QAAQ,GAAG,IAAI,CAACrC,QAAQ,CAACT,KAAK;IAEpC;IACA,IAAI,IAAI,CAACJ,SAAS,IAAI,CAACkD,QAAQ,CAAC9B,UAAU,EAAE;MAC1C,OAAO8B,QAAQ,CAAC9B,UAAU;;IAG5B,IAAI,IAAI,CAACpB,SAAS,IAAI,IAAI,CAACC,aAAa,EAAE;MACxC,IAAI,CAACkD,UAAU,CAAC,IAAI,CAAClD,aAAa,EAAEiD,QAAQ,CAAC;KAC9C,MAAM;MACL,IAAI,CAACE,UAAU,CAACF,QAAQ,CAAC;;EAE7B;EAEAE,UAAUA,CAACF,QAAa;IACtB,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,WAAW,CAAC8D,iBAAiB,CAACH,QAAQ,CAAC,CAAC1B,SAAS,CAAC;MACrDC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAI,CAACnC,KAAK,CAAC2D,IAAI,CAACxB,IAAI,CAAC;QACrB,IAAI,CAACJ,YAAY,EAAE;QACnB,IAAI,CAAC6B,WAAW,CAAC,8BAA8B,CAAC;QAChD,IAAI,CAACT,UAAU,EAAE;QACjB,IAAI,CAACjD,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,8CAA8C,CAAC;QAC9D,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAsD,UAAUA,CAACP,EAAU,EAAEM,QAAa;IAClC,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,WAAW,CAACiE,iBAAiB,CAACZ,EAAE,EAAEM,QAAQ,CAAC,CAAC1B,SAAS,CAAC;MACzDC,IAAI,EAAEA,CAAA,KAAK;QACT;QACA,IAAI,CAACb,SAAS,EAAE;QAChB,IAAI,CAAC2C,WAAW,CAAC,iCAAiC,CAAC;QACnD,IAAI,CAACT,UAAU,EAAE;QACjB,IAAI,CAACjD,OAAO,GAAG,KAAK;MACtB,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,kDAAkD,CAAC;QAClE,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA4D,UAAUA,CAAC3B,IAAS;IAClB;IACA,MAAM4B,WAAW,GAAG,IAAI,CAAClE,WAAW,CAACmE,cAAc,EAAE;IACrD,IAAID,WAAW,IAAIA,WAAW,CAACd,EAAE,KAAKd,IAAI,CAACc,EAAE,EAAE;MAC7C,IAAI,CAAChB,SAAS,CAAC,kDAAkD,CAAC;MAClE;;IAGF,IAAIgC,OAAO,CAAC,qDAAqD9B,IAAI,CAACf,GAAG,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAClB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACN,WAAW,CAACsE,iBAAiB,CAAC/B,IAAI,CAACc,EAAE,CAAC,CAACpB,SAAS,CAAC;QACpDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC9B,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkC,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKd,IAAI,CAACc,EAAE,CAAC;UACrD,IAAI,CAAClB,YAAY,EAAE;UACnB,IAAI,CAAC6B,WAAW,CAAC,kCAAkC,CAAC;UACpD,IAAI,CAAC1D,OAAO,GAAG,KAAK;QACtB,CAAC;QACD8B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,iDAAiD,CAAC;UACjE,IAAI,CAAC/B,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAkE,gBAAgBA,CAACjC,IAAS;IACxB,MAAMkC,SAAS,GAAG,CAAClC,IAAI,CAACR,KAAK;IAC7B,MAAM2C,MAAM,GAAGD,SAAS,GAAG,SAAS,GAAG,YAAY;IAEnD,IAAIJ,OAAO,CAAC,4BAA4BK,MAAM,mBAAmBnC,IAAI,CAACf,GAAG,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAClB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACN,WAAW,CAACiE,iBAAiB,CAAC1B,IAAI,CAACc,EAAE,EAAE;QAAEtB,KAAK,EAAE0C;MAAS,CAAE,CAAC,CAACxC,SAAS,CAAC;QAC1EC,IAAI,EAAEA,CAAA,KAAK;UACTK,IAAI,CAACR,KAAK,GAAG0C,SAAS;UACtB,IAAI,CAACT,WAAW,CAAC,eAAeS,SAAS,GAAG,QAAQ,GAAG,WAAW,cAAc,CAAC;UACjF,IAAI,CAACnE,OAAO,GAAG,KAAK;QACtB,CAAC;QACD8B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;UAC1D,IAAI,CAAC/B,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAqE,YAAYA,CAAC7C,IAAY;IACvB,MAAM8C,MAAM,GAAG,IAAI,CAAChE,WAAW,CAACiE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjE,KAAK,KAAKiB,IAAI,CAAC;IAC/D,OAAO8C,MAAM,GAAGA,MAAM,CAAC9D,KAAK,GAAGgB,IAAI;EACrC;EAEAiD,YAAYA,CAACjD,IAAY;IACvB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB;QACE,OAAO,EAAE;;EAEf;EAEAkD,aAAaA,CAACzC,IAAS;IACrB,MAAM4B,WAAW,GAAG,IAAI,CAAClE,WAAW,CAACmE,cAAc,EAAE;IACrD,OAAO,CAAC,EAAED,WAAW,IAAIA,WAAW,CAACd,EAAE,KAAKd,IAAI,CAACc,EAAE,CAAC;EACtD;EAEQK,oBAAoBA,CAAA;IAC1BuB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5D,QAAQ,CAAC6D,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAChD,MAAMC,OAAO,GAAG,IAAI,CAAChE,QAAQ,CAAC2B,GAAG,CAACoC,GAAG,CAAC;MACtCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQvB,WAAWA,CAACwB,OAAe;IACjC,IAAI,CAACrF,QAAQ,CAACe,IAAI,CAACsE,OAAO,EAAE,QAAQ,EAAE;MACpCrE,QAAQ,EAAE,IAAI;MACdsE,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQpD,SAASA,CAACmD,OAAe;IAC/B,IAAI,CAACrF,QAAQ,CAACe,IAAI,CAACsE,OAAO,EAAE,QAAQ,EAAE;MACpCrE,QAAQ,EAAE,IAAI;MACdsE,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAIjE,GAAGA,CAAA;IAAK,OAAO,IAAI,CAACF,QAAQ,CAAC2B,GAAG,CAAC,KAAK,CAAC;EAAE;EAC7C,IAAItB,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACL,QAAQ,CAAC2B,GAAG,CAAC,OAAO,CAAC;EAAE;EACjD,IAAIrB,QAAQA,CAAA;IAAK,OAAO,IAAI,CAACN,QAAQ,CAAC2B,GAAG,CAAC,UAAU,CAAC;EAAE;EACvD,IAAIpB,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACP,QAAQ,CAAC2B,GAAG,CAAC,YAAY,CAAC;EAAE;EAC3D,IAAInB,IAAIA,CAAA;IAAK,OAAO,IAAI,CAACR,QAAQ,CAAC2B,GAAG,CAAC,MAAM,CAAC;EAAE;EAC/C,IAAIlB,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACT,QAAQ,CAAC2B,GAAG,CAAC,OAAO,CAAC;EAAE;;;uBA/QtCnD,kBAAkB,EAAA4F,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBrG,kBAAkB;MAAAsG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ/Bf,EAAA,CAAAiB,cAAA,QAAG;UAAAjB,EAAA,CAAAkB,MAAA,wBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}