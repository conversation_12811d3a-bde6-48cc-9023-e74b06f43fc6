{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class RetenueALaSourceComponent {\n  static {\n    this.ɵfac = function RetenueALaSourceComponent_Factory(t) {\n      return new (t || RetenueALaSourceComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RetenueALaSourceComponent,\n      selectors: [[\"app-retenue-a-la-source\"]],\n      decls: 2,\n      vars: 0,\n      template: function RetenueALaSourceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"retenue-a-la-source works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RetenueALaSourceComponent", "selectors", "decls", "vars", "template", "RetenueALaSourceComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\retenue-a-la-source\\retenue-a-la-source.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\retenue-a-la-source\\retenue-a-la-source.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-retenue-a-la-source',\n  templateUrl: './retenue-a-la-source.component.html',\n  styleUrls: ['./retenue-a-la-source.component.css']\n})\nexport class RetenueALaSourceComponent {\n\n}\n", "<p>retenue-a-la-source works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPtCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,iCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}