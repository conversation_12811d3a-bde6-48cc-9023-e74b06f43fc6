{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\n// Angular Material Modules\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './login/login.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { RegisterComponent } from './register/register.component';\nimport { UsersidebarComponent } from './usersidebar/usersidebar.component';\nimport { AdminsidebarComponent } from './adminsidebar/adminsidebar.component';\nimport { AdmindashboardComponent } from './admindashboard/admindashboard.component';\nimport { UsersListComponent } from './users-list/users-list.component';\nimport { ClientsComponent } from './clients/clients.component';\nimport { FournisseursComponent } from './fournisseurs/fournisseurs.component';\nimport { RetenueALaSourceComponent } from './retenue-a-la-source/retenue-a-la-source.component';\nimport { UserProfileComponent } from './user-profile/user-profile.component';\nimport { ParametresSocietesComponent } from './parametres-societes/parametres-societes.component';\nimport { FacturesAchatComponent } from './factures-achat/factures-achat.component';\nimport { FacturesVenteComponent } from './factures-vente/factures-vente.component';\nimport { AdminDocumentsComponent } from './admin-documents/admin-documents.component';\nimport { UserDocumentsComponent } from './user-documents/user-documents.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, CommonModule, BrowserAnimationsModule, ReactiveFormsModule, FormsModule, HttpClientModule, RouterModule, AppRoutingModule,\n      // Angular Material Modules\n      MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatCardModule, MatTableModule, MatSelectModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatTooltipModule, MatDatepickerModule, MatNativeDateModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, DashboardComponent, RegisterComponent, UsersidebarComponent, AdminsidebarComponent, AdmindashboardComponent, UsersListComponent, ClientsComponent, FournisseursComponent, RetenueALaSourceComponent, UserProfileComponent, ParametresSocietesComponent, FacturesAchatComponent, FacturesVenteComponent, AdminDocumentsComponent, UserDocumentsComponent],\n    imports: [BrowserModule, CommonModule, BrowserAnimationsModule, ReactiveFormsModule, FormsModule, HttpClientModule, RouterModule, AppRoutingModule,\n    // Angular Material Modules\n    MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatCardModule, MatTableModule, MatSelectModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatTooltipModule, MatDatepickerModule, MatNativeDateModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "ReactiveFormsModule", "HttpClientModule", "RouterModule", "CommonModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatCheckboxModule", "MatCardModule", "MatTableModule", "MatSelectModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatDialogModule", "MatTooltipModule", "MatDatepickerModule", "MatNativeDateModule", "FormsModule", "AppRoutingModule", "AppComponent", "LoginComponent", "DashboardComponent", "RegisterComponent", "UsersidebarComponent", "AdminsidebarComponent", "AdmindashboardComponent", "UsersListComponent", "ClientsComponent", "FournisseursComponent", "RetenueALaSourceComponent", "UserProfileComponent", "ParametresSocietesComponent", "FacturesAchatComponent", "FacturesVenteComponent", "AdminDocumentsComponent", "UserDocumentsComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\n\n// Angular Material Modules\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { FormsModule } from '@angular/forms';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './login/login.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { RegisterComponent } from './register/register.component';\nimport { UsersidebarComponent } from './usersidebar/usersidebar.component';\nimport { AdminsidebarComponent } from './adminsidebar/adminsidebar.component';\nimport { AdmindashboardComponent } from './admindashboard/admindashboard.component';\nimport { UsersListComponent } from './users-list/users-list.component';\nimport { ClientsComponent } from './clients/clients.component';\nimport { FournisseursComponent } from './fournisseurs/fournisseurs.component';\nimport { RetenueALaSourceComponent } from './retenue-a-la-source/retenue-a-la-source.component';\nimport { UserProfileComponent } from './user-profile/user-profile.component';\nimport { ParametresSocietesComponent } from './parametres-societes/parametres-societes.component';\nimport { FacturesAchatComponent } from './factures-achat/factures-achat.component';\nimport { FacturesVenteComponent } from './factures-vente/factures-vente.component';\nimport { AdminDocumentsComponent } from './admin-documents/admin-documents.component';\nimport { UserDocumentsComponent } from './user-documents/user-documents.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    DashboardComponent,\n    RegisterComponent,\n    UsersidebarComponent,\n    AdminsidebarComponent,\n    AdmindashboardComponent,\n    UsersListComponent,\n    ClientsComponent,\n    FournisseursComponent,\n    RetenueALaSourceComponent,\n    UserProfileComponent,\n    ParametresSocietesComponent,\n    FacturesAchatComponent,\n    FacturesVenteComponent,\n    AdminDocumentsComponent,\n    UserDocumentsComponent\n  ],\n  imports: [\n    BrowserModule,\n    CommonModule,\n    BrowserAnimationsModule,\n    ReactiveFormsModule,\n    FormsModule,\n    HttpClientModule,\n    RouterModule,\n    AppRoutingModule,\n    // Angular Material Modules\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCheckboxModule,\n    MatCardModule,\n    MatTableModule,\n    MatSelectModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatDialogModule,\n    MatTooltipModule,\n    MatDatepickerModule,\n    MatNativeDateModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,uBAAuB,QAAQ,2CAA2C;AACnF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,yBAAyB,QAAQ,qDAAqD;AAC/F,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,sBAAsB,QAAQ,2CAA2C;;AAkDlF,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRlB,YAAY;IAAA;EAAA;;;gBAzBtBtB,aAAa,EACbK,YAAY,EACZJ,uBAAuB,EACvBC,mBAAmB,EACnBkB,WAAW,EACXjB,gBAAgB,EAChBC,YAAY,EACZiB,gBAAgB;MAChB;MACAf,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,EACnBC,mBAAmB;IAAA;EAAA;;;2EAKVoB,SAAS;IAAAE,YAAA,GA9ClBnB,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,oBAAoB,EACpBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,gBAAgB,EAChBC,qBAAqB,EACrBC,yBAAyB,EACzBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,sBAAsB,EACtBC,sBAAsB,EACtBC,uBAAuB,EACvBC,sBAAsB;IAAAI,OAAA,GAGtB1C,aAAa,EACbK,YAAY,EACZJ,uBAAuB,EACvBC,mBAAmB,EACnBkB,WAAW,EACXjB,gBAAgB,EAChBC,YAAY,EACZiB,gBAAgB;IAChB;IACAf,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,EACnBC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}