namespace stage.Services
{
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;
        private readonly Dictionary<string, string> _contentTypes;
        private readonly List<string> _allowedExtensions;

        public FileService(ILogger<FileService> logger)
        {
            _logger = logger;
            _contentTypes = new Dictionary<string, string>
            {
                { ".pdf", "application/pdf" },
                { ".jpg", "image/jpeg" },
                { ".jpeg", "image/jpeg" },
                { ".png", "image/png" },
                { ".gif", "image/gif" },
                { ".doc", "application/msword" },
                { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
                { ".xls", "application/vnd.ms-excel" },
                { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
                { ".txt", "text/plain" }
            };

            _allowedExtensions = new List<string>
            {
                ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".doc", ".docx", ".xls", ".xlsx", ".txt"
            };
        }

        public async Task<string> SaveFileAsync(IFormFile file, string subfolder = "uploads")
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("Fichier invalide");

            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
            var uploadsPath = Path.Combine(subfolder);
            Directory.CreateDirectory(uploadsPath);
            var filePath = Path.Combine(uploadsPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            _logger.LogInformation($"Fichier sauvegardé: {filePath}");
            return filePath;
        }

        public async Task<byte[]> GetFileAsync(string filePath)
        {
            if (!await FileExistsAsync(filePath))
                throw new FileNotFoundException($"Fichier non trouvé: {filePath}");

            return await File.ReadAllBytesAsync(filePath);
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                if (await FileExistsAsync(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation($"Fichier supprimé: {filePath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression du fichier: {filePath}");
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            return await Task.FromResult(File.Exists(filePath));
        }

        public string GetContentType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return _contentTypes.TryGetValue(extension, out var contentType) 
                ? contentType 
                : "application/octet-stream";
        }

        public async Task<string> SaveBase64FileAsync(string base64Content, string fileName, string subfolder = "uploads")
        {
            if (string.IsNullOrEmpty(base64Content))
                throw new ArgumentException("Contenu base64 invalide");

            var bytes = Convert.FromBase64String(base64Content);
            var uploadsPath = Path.Combine(subfolder);
            Directory.CreateDirectory(uploadsPath);
            var filePath = Path.Combine(uploadsPath, fileName);

            await File.WriteAllBytesAsync(filePath, bytes);
            _logger.LogInformation($"Fichier base64 sauvegardé: {filePath}");
            return filePath;
        }

        public async Task<List<string>> GetAllowedExtensions()
        {
            return await Task.FromResult(_allowedExtensions.ToList());
        }

        public async Task<bool> IsValidFileType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return await Task.FromResult(_allowedExtensions.Contains(extension));
        }

        public async Task<long> GetFileSizeAsync(string filePath)
        {
            if (!await FileExistsAsync(filePath))
                return 0;

            var fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }
    }
}
