{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nexport let UsersListComponent = /*#__PURE__*/(() => {\n  class UsersListComponent {\n    constructor(userService, authService, formBuilder, snackBar) {\n      this.userService = userService;\n      this.authService = authService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.users = []; // Utilisation de any car le service retourne des DTOs\n      this.filteredUsers = [];\n      this.loading = false;\n      this.searchTerm = '';\n      this.selectedRole = null;\n      this.isEditing = false;\n      this.editingUserId = null;\n      this.showForm = false;\n      // Options de rôles\n      this.roleOptions = [{\n        value: 'Admin',\n        label: 'Administrateur'\n      }, {\n        value: 'User',\n        label: 'Utilisateur'\n      }];\n      // Colonnes à afficher dans le tableau\n      this.displayedColumns = ['nom', 'email', 'userName', 'role', 'dateCreation', 'actif', 'actions'];\n    }\n    ngOnInit() {\n      // Vérifier les permissions - seuls les admins peuvent gérer les utilisateurs\n      if (!this.authService.canManageUsers()) {\n        this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      this.initializeForm();\n      this.loadUsers();\n    }\n    initializeForm() {\n      this.userForm = this.formBuilder.group({\n        nom: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        userName: ['', [Validators.required, Validators.minLength(3)]],\n        motDePasse: ['', [Validators.required, Validators.minLength(6)]],\n        role: ['User', [Validators.required]],\n        actif: [true]\n      });\n    }\n    loadUsers() {\n      this.loading = true;\n      this.userService.getUtilisateurs().subscribe({\n        next: users => {\n          this.users = users;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors du chargement des utilisateurs');\n          this.loading = false;\n        }\n      });\n    }\n    applyFilters() {\n      this.filteredUsers = this.users.filter(user => {\n        const matchesSearch = !this.searchTerm || user.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.userName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        const matchesRole = this.selectedRole === null || user.role === this.selectedRole;\n        return matchesSearch && matchesRole;\n      });\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    onRoleFilterChange() {\n      this.applyFilters();\n    }\n    showAddForm() {\n      this.isEditing = false;\n      this.editingUserId = null;\n      this.userForm.reset();\n      this.userForm.patchValue({\n        role: 'User',\n        actif: true\n      });\n      // Le mot de passe est requis pour la création\n      this.userForm.get('motDePasse')?.setValidators([Validators.required, Validators.minLength(6)]);\n      this.userForm.get('motDePasse')?.updateValueAndValidity();\n      this.showForm = true;\n    }\n    editUser(user) {\n      this.isEditing = true;\n      this.editingUserId = user.id;\n      this.userForm.patchValue({\n        nom: user.nom,\n        email: user.email,\n        userName: user.userName,\n        role: user.role,\n        actif: user.actif\n      });\n      // Le mot de passe n'est pas requis pour la modification\n      this.userForm.get('motDePasse')?.clearValidators();\n      this.userForm.get('motDePasse')?.updateValueAndValidity();\n      this.showForm = true;\n    }\n    cancelForm() {\n      this.showForm = false;\n      this.isEditing = false;\n      this.editingUserId = null;\n      this.userForm.reset();\n    }\n    onSubmit() {\n      if (this.userForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      const userData = this.userForm.value;\n      // Ne pas envoyer le mot de passe vide lors de la modification\n      if (this.isEditing && !userData.motDePasse) {\n        delete userData.motDePasse;\n      }\n      if (this.isEditing && this.editingUserId) {\n        this.updateUser(this.editingUserId, userData);\n      } else {\n        this.createUser(userData);\n      }\n    }\n    createUser(userData) {\n      this.loading = true;\n      this.userService.createUtilisateur(userData).subscribe({\n        next: user => {\n          this.users.push(user);\n          this.applyFilters();\n          this.showSuccess('Utilisateur créé avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la création de l\\'utilisateur');\n          this.loading = false;\n        }\n      });\n    }\n    updateUser(id, userData) {\n      this.loading = true;\n      this.userService.updateUtilisateur(id, userData).subscribe({\n        next: () => {\n          // Recharger la liste après modification\n          this.loadUsers();\n          this.showSuccess('Utilisateur modifié avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la modification de l\\'utilisateur');\n          this.loading = false;\n        }\n      });\n    }\n    deleteUser(user) {\n      // Empêcher la suppression de son propre compte\n      const currentUser = this.authService.getCurrentUser();\n      if (currentUser && currentUser.id === user.id) {\n        this.showError('Vous ne pouvez pas supprimer votre propre compte');\n        return;\n      }\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur \"${user.nom}\" ?`)) {\n        this.loading = true;\n        this.userService.deleteUtilisateur(user.id).subscribe({\n          next: () => {\n            this.users = this.users.filter(u => u.id !== user.id);\n            this.applyFilters();\n            this.showSuccess('Utilisateur supprimé avec succès');\n            this.loading = false;\n          },\n          error: error => {\n            this.showError('Erreur lors de la suppression de l\\'utilisateur');\n            this.loading = false;\n          }\n        });\n      }\n    }\n    toggleUserStatus(user) {\n      const newStatus = !user.actif;\n      const action = newStatus ? 'activer' : 'désactiver';\n      if (confirm(`Êtes-vous sûr de vouloir ${action} l'utilisateur \"${user.nom}\" ?`)) {\n        this.loading = true;\n        this.userService.updateUtilisateur(user.id, {\n          actif: newStatus\n        }).subscribe({\n          next: () => {\n            user.actif = newStatus;\n            this.showSuccess(`Utilisateur ${newStatus ? 'activé' : 'désactivé'} avec succès`);\n            this.loading = false;\n          },\n          error: error => {\n            this.showError(`Erreur lors de la modification du statut`);\n            this.loading = false;\n          }\n        });\n      }\n    }\n    getRoleLabel(role) {\n      const option = this.roleOptions.find(opt => opt.value === role);\n      return option ? option.label : role;\n    }\n    getRoleClass(role) {\n      switch (role) {\n        case 'Admin':\n          return 'role-admin';\n        case 'User':\n          return 'role-user';\n        default:\n          return '';\n      }\n    }\n    canDeleteUser(user) {\n      const currentUser = this.authService.getCurrentUser();\n      return currentUser && currentUser.id !== user.id;\n    }\n    markFormGroupTouched() {\n      Object.keys(this.userForm.controls).forEach(key => {\n        const control = this.userForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    showSuccess(message) {\n      this.snackBar.open(message, 'Fermer', {\n        duration: 3000,\n        panelClass: ['success-snackbar']\n      });\n    }\n    showError(message) {\n      this.snackBar.open(message, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    // Getters pour faciliter l'accès aux contrôles dans le template\n    get nom() {\n      return this.userForm.get('nom');\n    }\n    get email() {\n      return this.userForm.get('email');\n    }\n    get userName() {\n      return this.userForm.get('userName');\n    }\n    get motDePasse() {\n      return this.userForm.get('motDePasse');\n    }\n    get role() {\n      return this.userForm.get('role');\n    }\n    get actif() {\n      return this.userForm.get('actif');\n    }\n    static {\n      this.ɵfac = function UsersListComponent_Factory(t) {\n        return new (t || UsersListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UsersListComponent,\n        selectors: [[\"app-users-list\"]],\n        decls: 2,\n        vars: 0,\n        template: function UsersListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"users-list works!\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return UsersListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}