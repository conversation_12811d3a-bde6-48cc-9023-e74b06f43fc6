{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ClientService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:5251/api/clients';\n  }\n  getClients() {\n    return this.http.get(this.API_URL);\n  }\n  getClient(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  createClient(client) {\n    return this.http.post(this.API_URL, client);\n  }\n  updateClient(id, client) {\n    return this.http.put(`${this.API_URL}/${id}`, client);\n  }\n  deleteClient(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  static {\n    this.ɵfac = function ClientService_Factory(t) {\n      return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ClientService,\n      factory: ClientService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ClientService", "constructor", "http", "API_URL", "getClients", "get", "getClient", "id", "createClient", "client", "post", "updateClient", "put", "deleteClient", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Client } from 'src/app/models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private readonly API_URL = 'http://localhost:5251/api/clients';\n\n  constructor(private http: HttpClient) {}\n\n  getClients(): Observable<Client[]> {\n    return this.http.get<Client[]>(this.API_URL);\n  }\n\n  getClient(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.API_URL}/${id}`);\n  }\n\n  createClient(client: Partial<Client>): Observable<Client> {\n    return this.http.post<Client>(this.API_URL, client);\n  }\n\n  updateClient(id: string, client: Partial<Client>): Observable<Client> {\n    return this.http.put<Client>(`${this.API_URL}/${id}`, client);\n  }\n\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Ces endpoints n'existent pas dans le backend\n  // Seuls les CRUD de base sont disponibles\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAG,mCAAmC;EAEvB;EAEvCC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAW,IAAI,CAACF,OAAO,CAAC;EAC9C;EAEAG,SAASA,CAACC,EAAU;IAClB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,OAAO,IAAII,EAAE,EAAE,CAAC;EACvD;EAEAC,YAAYA,CAACC,MAAuB;IAClC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAS,IAAI,CAACP,OAAO,EAAEM,MAAM,CAAC;EACrD;EAEAE,YAAYA,CAACJ,EAAU,EAAEE,MAAuB;IAC9C,OAAO,IAAI,CAACP,IAAI,CAACU,GAAG,CAAS,GAAG,IAAI,CAACT,OAAO,IAAII,EAAE,EAAE,EAAEE,MAAM,CAAC;EAC/D;EAEAI,YAAYA,CAACN,EAAU;IACrB,OAAO,IAAI,CAACL,IAAI,CAACY,MAAM,CAAO,GAAG,IAAI,CAACX,OAAO,IAAII,EAAE,EAAE,CAAC;EACxD;;;uBAvBWP,aAAa,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAblB,aAAa;MAAAmB,OAAA,EAAbnB,aAAa,CAAAoB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}