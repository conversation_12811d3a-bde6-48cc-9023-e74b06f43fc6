{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { StatutFacture } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/facture-achat.service\";\nimport * as i2 from \"../../services/fournisseur.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/snack-bar\";\nexport class FacturesAchatComponent {\n  constructor(factureAchatService, fournisseurService, authService, formBuilder, snackBar) {\n    this.factureAchatService = factureAchatService;\n    this.fournisseurService = fournisseurService;\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.factures = [];\n    this.filteredFactures = [];\n    this.fournisseurs = [];\n    this.loading = false;\n    this.searchTerm = '';\n    this.selectedStatut = null;\n    this.selectedFournisseur = null;\n    this.isEditing = false;\n    this.editingFactureId = null;\n    this.showForm = false;\n    // Gestion des fichiers\n    this.selectedFile = null;\n    this.filePreview = null;\n    this.dragOver = false;\n    // Énumérations pour le template\n    this.StatutFacture = StatutFacture;\n    this.statutOptions = [{\n      value: StatutFacture.Brouillon,\n      label: 'Brouillon'\n    }, {\n      value: StatutFacture.Envoyee,\n      label: 'Envoyée'\n    }, {\n      value: StatutFacture.Payee,\n      label: 'Payée'\n    }, {\n      value: StatutFacture.EnRetard,\n      label: 'En retard'\n    }, {\n      value: StatutFacture.Annulee,\n      label: 'Annulée'\n    }];\n    // Types de fichiers acceptés\n    this.acceptedFileTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.gif'];\n    this.maxFileSize = 10 * 1024 * 1024; // 10MB\n    // Colonnes à afficher dans le tableau\n    this.displayedColumns = ['numero', 'fournisseur', 'montant', 'date', 'dateEcheance', 'statut', 'fichier', 'actions'];\n  }\n  ngOnInit() {\n    // Vérifier les permissions\n    if (!this.authService.canCreateInvoices()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', {\n        duration: 3000\n      });\n      return;\n    }\n    this.initializeForm();\n    this.loadFournisseurs();\n    this.loadFactures();\n  }\n  initializeForm() {\n    this.factureForm = this.formBuilder.group({\n      numero: ['', [Validators.required]],\n      fournisseurId: ['', [Validators.required]],\n      montant: ['', [Validators.required, Validators.min(0.01)]],\n      date: [new Date(), [Validators.required]],\n      dateEcheance: [''],\n      statut: [StatutFacture.Brouillon, [Validators.required]],\n      notesInternes: ['']\n    });\n  }\n  loadFournisseurs() {\n    this.fournisseurService.getFournisseurs().subscribe({\n      next: fournisseurs => {\n        this.fournisseurs = fournisseurs.filter(f => f.statut === 0); // Seulement les fournisseurs actifs\n      },\n\n      error: error => {\n        this.showError('Erreur lors du chargement des fournisseurs');\n      }\n    });\n  }\n  loadFactures() {\n    this.loading = true;\n    this.factureAchatService.getFacturesAchat().subscribe({\n      next: factures => {\n        this.factures = factures;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des factures');\n        this.loading = false;\n      }\n    });\n  }\n  applyFilters() {\n    this.filteredFactures = this.factures.filter(facture => {\n      const matchesSearch = !this.searchTerm || facture.numero.toLowerCase().includes(this.searchTerm.toLowerCase()) || facture.fournisseur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesStatut = this.selectedStatut === null || facture.statut === this.selectedStatut;\n      const matchesFournisseur = this.selectedFournisseur === null || facture.fournisseurId === this.selectedFournisseur;\n      return matchesSearch && matchesStatut && matchesFournisseur;\n    });\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  onStatutFilterChange() {\n    this.applyFilters();\n  }\n  onFournisseurFilterChange() {\n    this.applyFilters();\n  }\n  showAddForm() {\n    this.isEditing = false;\n    this.editingFactureId = null;\n    this.factureForm.reset();\n    this.factureForm.patchValue({\n      date: new Date(),\n      statut: StatutFacture.Brouillon\n    });\n    this.generateFactureNumber();\n    this.resetFileSelection();\n    this.showForm = true;\n  }\n  editFacture(facture) {\n    this.isEditing = true;\n    this.editingFactureId = facture.id;\n    this.factureForm.patchValue({\n      numero: facture.numero,\n      fournisseurId: facture.fournisseurId,\n      montant: facture.montant,\n      date: new Date(facture.date),\n      dateEcheance: facture.dateEcheance ? new Date(facture.dateEcheance) : null,\n      statut: facture.statut,\n      notesInternes: facture.notesInternes\n    });\n    this.resetFileSelection();\n    this.showForm = true;\n  }\n  cancelForm() {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingFactureId = null;\n    this.factureForm.reset();\n    this.resetFileSelection();\n  }\n  // Gestion des fichiers\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleFileSelection(file);\n    }\n  }\n  onFileDrop(event) {\n    event.preventDefault();\n    this.dragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      this.handleFileSelection(files[0]);\n    }\n  }\n  onDragOver(event) {\n    event.preventDefault();\n    this.dragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    this.dragOver = false;\n  }\n  handleFileSelection(file) {\n    // Vérifier le type de fichier\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.acceptedFileTypes.includes(fileExtension)) {\n      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedFileTypes.join(', '));\n      return;\n    }\n    // Vérifier la taille du fichier\n    if (file.size > this.maxFileSize) {\n      this.showError('Fichier trop volumineux. Taille maximale: 10MB');\n      return;\n    }\n    this.selectedFile = file;\n    // Créer un aperçu pour les images\n    if (file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.filePreview = e.target?.result;\n      };\n      reader.readAsDataURL(file);\n    } else {\n      this.filePreview = null;\n    }\n  }\n  removeFile() {\n    this.resetFileSelection();\n  }\n  resetFileSelection() {\n    this.selectedFile = null;\n    this.filePreview = null;\n    this.dragOver = false;\n  }\n  onSubmit() {\n    if (this.factureForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    const factureData = this.factureForm.value;\n    if (this.isEditing && this.editingFactureId) {\n      this.updateFacture(this.editingFactureId, factureData);\n    } else {\n      this.createFacture(factureData);\n    }\n  }\n  createFacture(factureData) {\n    this.loading = true;\n    // Si un fichier est sélectionné, l'inclure dans les données\n    if (this.selectedFile) {\n      const formData = new FormData();\n      Object.keys(factureData).forEach(key => {\n        formData.append(key, factureData[key]);\n      });\n      formData.append('fichier', this.selectedFile);\n      this.factureAchatService.createFactureAchatWithFile(formData).subscribe({\n        next: facture => {\n          this.factures.push(facture);\n          this.applyFilters();\n          this.showSuccess('Facture créée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la création de la facture');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.factureAchatService.createFactureAchat(factureData).subscribe({\n        next: facture => {\n          this.factures.push(facture);\n          this.applyFilters();\n          this.showSuccess('Facture créée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la création de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  updateFacture(id, factureData) {\n    this.loading = true;\n    // Si un nouveau fichier est sélectionné, l'inclure dans la mise à jour\n    if (this.selectedFile) {\n      const formData = new FormData();\n      Object.keys(factureData).forEach(key => {\n        formData.append(key, factureData[key]);\n      });\n      formData.append('fichier', this.selectedFile);\n      this.factureAchatService.updateFactureAchatWithFile(id, formData).subscribe({\n        next: updatedFacture => {\n          const index = this.factures.findIndex(f => f.id === id);\n          if (index !== -1) {\n            this.factures[index] = updatedFacture;\n            this.applyFilters();\n          }\n          this.showSuccess('Facture modifiée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la modification de la facture');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.factureAchatService.updateFactureAchat(id, factureData).subscribe({\n        next: updatedFacture => {\n          const index = this.factures.findIndex(f => f.id === id);\n          if (index !== -1) {\n            this.factures[index] = updatedFacture;\n            this.applyFilters();\n          }\n          this.showSuccess('Facture modifiée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la modification de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  deleteFacture(facture) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la facture \"${facture.numero}\" ?`)) {\n      this.loading = true;\n      this.factureAchatService.deleteFactureAchat(facture.id).subscribe({\n        next: () => {\n          this.factures = this.factures.filter(f => f.id !== facture.id);\n          this.applyFilters();\n          this.showSuccess('Facture supprimée avec succès');\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la suppression de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  downloadFile(facture) {\n    if (facture.cheminFichier) {\n      this.factureAchatService.downloadFile(facture.id).subscribe({\n        next: blob => {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = facture.nomFichierOriginal || 'facture.pdf';\n          link.click();\n          window.URL.revokeObjectURL(url);\n        },\n        error: error => {\n          this.showError('Erreur lors du téléchargement du fichier');\n        }\n      });\n    }\n  }\n  generateFactureNumber() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');\n    const numero = `FA-${year}${month}${day}-${time}`;\n    this.factureForm.patchValue({\n      numero\n    });\n  }\n  getStatutLabel(statut) {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n  getStatutClass(statut) {\n    switch (statut) {\n      case StatutFacture.Brouillon:\n        return 'statut-brouillon';\n      case StatutFacture.Envoyee:\n        return 'statut-envoyee';\n      case StatutFacture.Payee:\n        return 'statut-payee';\n      case StatutFacture.EnRetard:\n        return 'statut-retard';\n      case StatutFacture.Annulee:\n        return 'statut-annulee';\n      default:\n        return '';\n    }\n  }\n  getFournisseurName(fournisseurId) {\n    const fournisseur = this.fournisseurs.find(f => f.id === fournisseurId);\n    return fournisseur ? fournisseur.nom : 'Fournisseur inconnu';\n  }\n  hasFile(facture) {\n    return !!(facture.cheminFichier && facture.nomFichierOriginal);\n  }\n  getFileSize(sizeInBytes) {\n    if (!sizeInBytes) return '';\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = sizeInBytes;\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  }\n  markFormGroupTouched() {\n    Object.keys(this.factureForm.controls).forEach(key => {\n      const control = this.factureForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get numero() {\n    return this.factureForm.get('numero');\n  }\n  get fournisseurId() {\n    return this.factureForm.get('fournisseurId');\n  }\n  get montant() {\n    return this.factureForm.get('montant');\n  }\n  get date() {\n    return this.factureForm.get('date');\n  }\n  get dateEcheance() {\n    return this.factureForm.get('dateEcheance');\n  }\n  get statut() {\n    return this.factureForm.get('statut');\n  }\n  get notesInternes() {\n    return this.factureForm.get('notesInternes');\n  }\n  static {\n    this.ɵfac = function FacturesAchatComponent_Factory(t) {\n      return new (t || FacturesAchatComponent)(i0.ɵɵdirectiveInject(i1.FactureAchatService), i0.ɵɵdirectiveInject(i2.FournisseurService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FacturesAchatComponent,\n      selectors: [[\"app-factures-achat\"]],\n      decls: 2,\n      vars: 0,\n      template: function FacturesAchatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"factures-achat works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "StatutFacture", "FacturesAchatComponent", "constructor", "factureAchatService", "fournisseurService", "authService", "formBuilder", "snackBar", "factures", "filteredFactures", "fournisseurs", "loading", "searchTerm", "selectedStatut", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEditing", "editingFactureId", "showForm", "selectedFile", "filePreview", "dragOver", "statutOptions", "value", "Brouillon", "label", "Envoyee", "Payee", "EnRetard", "<PERSON><PERSON><PERSON>", "acceptedFileTypes", "maxFileSize", "displayedColumns", "ngOnInit", "canCreateInvoices", "open", "duration", "initializeForm", "loadFournisseurs", "loadFactures", "factureForm", "group", "numero", "required", "fournisseurId", "montant", "min", "date", "Date", "dateEcheance", "statut", "notesInternes", "getFournisseurs", "subscribe", "next", "filter", "f", "error", "showError", "getFacturesAchat", "applyFilters", "facture", "matchesSearch", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "nom", "matchesStatut", "matchesFournisseur", "onSearchChange", "onStatutFilterChange", "onFournisseurFilterChange", "showAddForm", "reset", "patchValue", "generateFactureNumber", "resetFileSelection", "editFacture", "id", "cancelForm", "onFileSelected", "event", "file", "target", "files", "handleFileSelection", "onFileDrop", "preventDefault", "dataTransfer", "length", "onDragOver", "onDragLeave", "fileExtension", "name", "split", "pop", "join", "size", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeFile", "onSubmit", "invalid", "markFormGroupTouched", "factureData", "updateFacture", "createFacture", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "createFactureAchatWithFile", "push", "showSuccess", "createFactureAchat", "updateFactureAchatWithFile", "updatedFacture", "index", "findIndex", "updateFactureAchat", "deleteFacture", "confirm", "deleteFactureAchat", "downloadFile", "chemin<PERSON><PERSON><PERSON>", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "nomFichierOriginal", "click", "revokeObjectURL", "now", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "time", "getHours", "getMinutes", "getStatutLabel", "option", "find", "opt", "getStatutClass", "getFournisseurName", "hasFile", "getFileSize", "sizeInBytes", "units", "unitIndex", "toFixed", "controls", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "panelClass", "i0", "ɵɵdirectiveInject", "i1", "FactureAchatService", "i2", "FournisseurService", "i3", "AuthService", "i4", "FormBuilder", "i5", "MatSnackBar", "selectors", "decls", "vars", "template", "FacturesAchatComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\factures-achat\\factures-achat.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\factures-achat\\factures-achat.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { FactureAchatService } from '../../services/facture-achat.service';\nimport { FournisseurService } from '../../services/fournisseur.service';\nimport { AuthService } from '../../services/auth.service';\nimport { FactureAchat, StatutFacture, Fournisseur } from '../models';\n\n@Component({\n  selector: 'app-factures-achat',\n  templateUrl: './factures-achat.component.html',\n  styleUrls: ['./factures-achat.component.css']\n})\nexport class FacturesAchatComponent implements OnInit {\n  factures: FactureAchat[] = [];\n  filteredFactures: FactureAchat[] = [];\n  fournisseurs: Fournisseur[] = [];\n  loading = false;\n  searchTerm = '';\n  selectedStatut: StatutFacture | null = null;\n  selectedFournisseur: string | null = null;\n\n  // Formulaire pour ajouter/modifier une facture\n  factureForm!: FormGroup;\n  isEditing = false;\n  editingFactureId: string | null = null;\n  showForm = false;\n\n  // Gestion des fichiers\n  selectedFile: File | null = null;\n  filePreview: string | null = null;\n  dragOver = false;\n\n  // Énumérations pour le template\n  StatutFacture = StatutFacture;\n  statutOptions = [\n    { value: StatutFacture.Brouillon, label: 'Brouillon' },\n    { value: StatutFacture.Envoyee, label: 'Envoyée' },\n    { value: StatutFacture.Payee, label: 'Payée' },\n    { value: StatutFacture.EnRetard, label: 'En retard' },\n    { value: StatutFacture.Annulee, label: 'Annulée' }\n  ];\n\n  // Types de fichiers acceptés\n  acceptedFileTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.gif'];\n  maxFileSize = 10 * 1024 * 1024; // 10MB\n\n  // Colonnes à afficher dans le tableau\n  displayedColumns: string[] = ['numero', 'fournisseur', 'montant', 'date', 'dateEcheance', 'statut', 'fichier', 'actions'];\n\n  constructor(\n    private factureAchatService: FactureAchatService,\n    private fournisseurService: FournisseurService,\n    private authService: AuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Vérifier les permissions\n    if (!this.authService.canCreateInvoices()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.initializeForm();\n    this.loadFournisseurs();\n    this.loadFactures();\n  }\n\n  initializeForm(): void {\n    this.factureForm = this.formBuilder.group({\n      numero: ['', [Validators.required]],\n      fournisseurId: ['', [Validators.required]],\n      montant: ['', [Validators.required, Validators.min(0.01)]],\n      date: [new Date(), [Validators.required]],\n      dateEcheance: [''],\n      statut: [StatutFacture.Brouillon, [Validators.required]],\n      notesInternes: ['']\n    });\n  }\n\n  loadFournisseurs(): void {\n    this.fournisseurService.getFournisseurs().subscribe({\n      next: (fournisseurs) => {\n        this.fournisseurs = fournisseurs.filter(f => f.statut === 0); // Seulement les fournisseurs actifs\n      },\n      error: (error) => {\n        this.showError('Erreur lors du chargement des fournisseurs');\n      }\n    });\n  }\n\n  loadFactures(): void {\n    this.loading = true;\n    this.factureAchatService.getFacturesAchat().subscribe({\n      next: (factures) => {\n        this.factures = factures;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors du chargement des factures');\n        this.loading = false;\n      }\n    });\n  }\n\n  applyFilters(): void {\n    this.filteredFactures = this.factures.filter(facture => {\n      const matchesSearch = !this.searchTerm ||\n        facture.numero.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        (facture.fournisseur?.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));\n\n      const matchesStatut = this.selectedStatut === null || facture.statut === this.selectedStatut;\n      const matchesFournisseur = this.selectedFournisseur === null || facture.fournisseurId === this.selectedFournisseur;\n\n      return matchesSearch && matchesStatut && matchesFournisseur;\n    });\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  onStatutFilterChange(): void {\n    this.applyFilters();\n  }\n\n  onFournisseurFilterChange(): void {\n    this.applyFilters();\n  }\n\n  showAddForm(): void {\n    this.isEditing = false;\n    this.editingFactureId = null;\n    this.factureForm.reset();\n    this.factureForm.patchValue({\n      date: new Date(),\n      statut: StatutFacture.Brouillon\n    });\n    this.generateFactureNumber();\n    this.resetFileSelection();\n    this.showForm = true;\n  }\n\n  editFacture(facture: FactureAchat): void {\n    this.isEditing = true;\n    this.editingFactureId = facture.id;\n    this.factureForm.patchValue({\n      numero: facture.numero,\n      fournisseurId: facture.fournisseurId,\n      montant: facture.montant,\n      date: new Date(facture.date),\n      dateEcheance: facture.dateEcheance ? new Date(facture.dateEcheance) : null,\n      statut: facture.statut,\n      notesInternes: facture.notesInternes\n    });\n    this.resetFileSelection();\n    this.showForm = true;\n  }\n\n  cancelForm(): void {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingFactureId = null;\n    this.factureForm.reset();\n    this.resetFileSelection();\n  }\n\n  // Gestion des fichiers\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleFileSelection(file);\n    }\n  }\n\n  onFileDrop(event: DragEvent): void {\n    event.preventDefault();\n    this.dragOver = false;\n\n    const files = event.dataTransfer?.files;\n    if (files && files.length > 0) {\n      this.handleFileSelection(files[0]);\n    }\n  }\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    this.dragOver = true;\n  }\n\n  onDragLeave(event: DragEvent): void {\n    event.preventDefault();\n    this.dragOver = false;\n  }\n\n  handleFileSelection(file: File): void {\n    // Vérifier le type de fichier\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.acceptedFileTypes.includes(fileExtension)) {\n      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedFileTypes.join(', '));\n      return;\n    }\n\n    // Vérifier la taille du fichier\n    if (file.size > this.maxFileSize) {\n      this.showError('Fichier trop volumineux. Taille maximale: 10MB');\n      return;\n    }\n\n    this.selectedFile = file;\n\n    // Créer un aperçu pour les images\n    if (file.type.startsWith('image/')) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        this.filePreview = e.target?.result as string;\n      };\n      reader.readAsDataURL(file);\n    } else {\n      this.filePreview = null;\n    }\n  }\n\n  removeFile(): void {\n    this.resetFileSelection();\n  }\n\n  resetFileSelection(): void {\n    this.selectedFile = null;\n    this.filePreview = null;\n    this.dragOver = false;\n  }\n\n  onSubmit(): void {\n    if (this.factureForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    const factureData = this.factureForm.value;\n\n    if (this.isEditing && this.editingFactureId) {\n      this.updateFacture(this.editingFactureId, factureData);\n    } else {\n      this.createFacture(factureData);\n    }\n  }\n\n  createFacture(factureData: any): void {\n    this.loading = true;\n\n    // Si un fichier est sélectionné, l'inclure dans les données\n    if (this.selectedFile) {\n      const formData = new FormData();\n      Object.keys(factureData).forEach(key => {\n        formData.append(key, factureData[key]);\n      });\n      formData.append('fichier', this.selectedFile);\n\n      this.factureAchatService.createFactureAchatWithFile(formData).subscribe({\n        next: (facture) => {\n          this.factures.push(facture);\n          this.applyFilters();\n          this.showSuccess('Facture créée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la création de la facture');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.factureAchatService.createFactureAchat(factureData).subscribe({\n        next: (facture) => {\n          this.factures.push(facture);\n          this.applyFilters();\n          this.showSuccess('Facture créée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la création de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  updateFacture(id: string, factureData: any): void {\n    this.loading = true;\n\n    // Si un nouveau fichier est sélectionné, l'inclure dans la mise à jour\n    if (this.selectedFile) {\n      const formData = new FormData();\n      Object.keys(factureData).forEach(key => {\n        formData.append(key, factureData[key]);\n      });\n      formData.append('fichier', this.selectedFile);\n\n      this.factureAchatService.updateFactureAchatWithFile(id, formData).subscribe({\n        next: (updatedFacture) => {\n          const index = this.factures.findIndex(f => f.id === id);\n          if (index !== -1) {\n            this.factures[index] = updatedFacture;\n            this.applyFilters();\n          }\n          this.showSuccess('Facture modifiée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la modification de la facture');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.factureAchatService.updateFactureAchat(id, factureData).subscribe({\n        next: (updatedFacture) => {\n          const index = this.factures.findIndex(f => f.id === id);\n          if (index !== -1) {\n            this.factures[index] = updatedFacture;\n            this.applyFilters();\n          }\n          this.showSuccess('Facture modifiée avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la modification de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  deleteFacture(facture: FactureAchat): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la facture \"${facture.numero}\" ?`)) {\n      this.loading = true;\n      this.factureAchatService.deleteFactureAchat(facture.id).subscribe({\n        next: () => {\n          this.factures = this.factures.filter(f => f.id !== facture.id);\n          this.applyFilters();\n          this.showSuccess('Facture supprimée avec succès');\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la suppression de la facture');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  downloadFile(facture: FactureAchat): void {\n    if (facture.cheminFichier) {\n      this.factureAchatService.downloadFile(facture.id).subscribe({\n        next: (blob) => {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = facture.nomFichierOriginal || 'facture.pdf';\n          link.click();\n          window.URL.revokeObjectURL(url);\n        },\n        error: (error) => {\n          this.showError('Erreur lors du téléchargement du fichier');\n        }\n      });\n    }\n  }\n\n  generateFactureNumber(): void {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');\n\n    const numero = `FA-${year}${month}${day}-${time}`;\n    this.factureForm.patchValue({ numero });\n  }\n\n  getStatutLabel(statut: StatutFacture): string {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n\n  getStatutClass(statut: StatutFacture): string {\n    switch (statut) {\n      case StatutFacture.Brouillon:\n        return 'statut-brouillon';\n      case StatutFacture.Envoyee:\n        return 'statut-envoyee';\n      case StatutFacture.Payee:\n        return 'statut-payee';\n      case StatutFacture.EnRetard:\n        return 'statut-retard';\n      case StatutFacture.Annulee:\n        return 'statut-annulee';\n      default:\n        return '';\n    }\n  }\n\n  getFournisseurName(fournisseurId: string): string {\n    const fournisseur = this.fournisseurs.find(f => f.id === fournisseurId);\n    return fournisseur ? fournisseur.nom : 'Fournisseur inconnu';\n  }\n\n  hasFile(facture: FactureAchat): boolean {\n    return !!(facture.cheminFichier && facture.nomFichierOriginal);\n  }\n\n  getFileSize(sizeInBytes: number | undefined): string {\n    if (!sizeInBytes) return '';\n\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = sizeInBytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.factureForm.controls).forEach(key => {\n      const control = this.factureForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get numero() { return this.factureForm.get('numero'); }\n  get fournisseurId() { return this.factureForm.get('fournisseurId'); }\n  get montant() { return this.factureForm.get('montant'); }\n  get date() { return this.factureForm.get('date'); }\n  get dateEcheance() { return this.factureForm.get('dateEcheance'); }\n  get statut() { return this.factureForm.get('statut'); }\n  get notesInternes() { return this.factureForm.get('notesInternes'); }\n}\n", "<p>factures-achat works!</p>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAAuBC,aAAa,QAAqB,WAAW;;;;;;;AAOpE,OAAM,MAAOC,sBAAsB;EAqCjCC,YACUC,mBAAwC,EACxCC,kBAAsC,EACtCC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAzClB,KAAAC,QAAQ,GAAmB,EAAE;IAC7B,KAAAC,gBAAgB,GAAmB,EAAE;IACrC,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,cAAc,GAAyB,IAAI;IAC3C,KAAAC,mBAAmB,GAAkB,IAAI;IAIzC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,WAAW,GAAkB,IAAI;IACjC,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAApB,aAAa,GAAGA,aAAa;IAC7B,KAAAqB,aAAa,GAAG,CACd;MAAEC,KAAK,EAAEtB,aAAa,CAACuB,SAAS;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtD;MAAEF,KAAK,EAAEtB,aAAa,CAACyB,OAAO;MAAED,KAAK,EAAE;IAAS,CAAE,EAClD;MAAEF,KAAK,EAAEtB,aAAa,CAAC0B,KAAK;MAAEF,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAEF,KAAK,EAAEtB,aAAa,CAAC2B,QAAQ;MAAEH,KAAK,EAAE;IAAW,CAAE,EACrD;MAAEF,KAAK,EAAEtB,aAAa,CAAC4B,OAAO;MAAEJ,KAAK,EAAE;IAAS,CAAE,CACnD;IAED;IACA,KAAAK,iBAAiB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7D,KAAAC,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAEhC;IACA,KAAAC,gBAAgB,GAAa,CAAC,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;EAQtH;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,iBAAiB,EAAE,EAAE;MACzC,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAAC,kDAAkD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpG;;IAGF,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAF,cAAcA,CAAA;IACZ,IAAI,CAACG,WAAW,GAAG,IAAI,CAACjC,WAAW,CAACkC,KAAK,CAAC;MACxCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAAC2C,QAAQ,CAAC,CAAC;MACnCC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC2C,QAAQ,CAAC,CAAC;MAC1CE,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC8C,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;MAC1DC,IAAI,EAAE,CAAC,IAAIC,IAAI,EAAE,EAAE,CAAChD,UAAU,CAAC2C,QAAQ,CAAC,CAAC;MACzCM,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,MAAM,EAAE,CAACjD,aAAa,CAACuB,SAAS,EAAE,CAACxB,UAAU,CAAC2C,QAAQ,CAAC,CAAC;MACxDQ,aAAa,EAAE,CAAC,EAAE;KACnB,CAAC;EACJ;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAACjC,kBAAkB,CAAC+C,eAAe,EAAE,CAACC,SAAS,CAAC;MAClDC,IAAI,EAAG3C,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACN,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC;;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,4CAA4C,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAnB,YAAYA,CAAA;IACV,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACR,mBAAmB,CAACuD,gBAAgB,EAAE,CAACN,SAAS,CAAC;MACpDC,IAAI,EAAG7C,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACmD,YAAY,EAAE;QACnB,IAAI,CAAChD,OAAO,GAAG,KAAK;MACtB,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,wCAAwC,CAAC;QACxD,IAAI,CAAC9C,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAgD,YAAYA,CAAA;IACV,IAAI,CAAClD,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAAC8C,MAAM,CAACM,OAAO,IAAG;MACrD,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACjD,UAAU,IACpCgD,OAAO,CAACnB,MAAM,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnD,UAAU,CAACkD,WAAW,EAAE,CAAC,IACnEF,OAAO,CAACI,WAAW,EAAEC,GAAG,CAACH,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnD,UAAU,CAACkD,WAAW,EAAE,CAAE;MAElF,MAAMI,aAAa,GAAG,IAAI,CAACrD,cAAc,KAAK,IAAI,IAAI+C,OAAO,CAACX,MAAM,KAAK,IAAI,CAACpC,cAAc;MAC5F,MAAMsD,kBAAkB,GAAG,IAAI,CAACrD,mBAAmB,KAAK,IAAI,IAAI8C,OAAO,CAACjB,aAAa,KAAK,IAAI,CAAC7B,mBAAmB;MAElH,OAAO+C,aAAa,IAAIK,aAAa,IAAIC,kBAAkB;IAC7D,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACT,YAAY,EAAE;EACrB;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,CAACV,YAAY,EAAE;EACrB;EAEAW,yBAAyBA,CAAA;IACvB,IAAI,CAACX,YAAY,EAAE;EACrB;EAEAY,WAAWA,CAAA;IACT,IAAI,CAACxD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACuB,WAAW,CAACiC,KAAK,EAAE;IACxB,IAAI,CAACjC,WAAW,CAACkC,UAAU,CAAC;MAC1B3B,IAAI,EAAE,IAAIC,IAAI,EAAE;MAChBE,MAAM,EAAEjD,aAAa,CAACuB;KACvB,CAAC;IACF,IAAI,CAACmD,qBAAqB,EAAE;IAC5B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;EACtB;EAEA2D,WAAWA,CAAChB,OAAqB;IAC/B,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,gBAAgB,GAAG4C,OAAO,CAACiB,EAAE;IAClC,IAAI,CAACtC,WAAW,CAACkC,UAAU,CAAC;MAC1BhC,MAAM,EAAEmB,OAAO,CAACnB,MAAM;MACtBE,aAAa,EAAEiB,OAAO,CAACjB,aAAa;MACpCC,OAAO,EAAEgB,OAAO,CAAChB,OAAO;MACxBE,IAAI,EAAE,IAAIC,IAAI,CAACa,OAAO,CAACd,IAAI,CAAC;MAC5BE,YAAY,EAAEY,OAAO,CAACZ,YAAY,GAAG,IAAID,IAAI,CAACa,OAAO,CAACZ,YAAY,CAAC,GAAG,IAAI;MAC1EC,MAAM,EAAEW,OAAO,CAACX,MAAM;MACtBC,aAAa,EAAEU,OAAO,CAACV;KACxB,CAAC;IACF,IAAI,CAACyB,kBAAkB,EAAE;IACzB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;EACtB;EAEA6D,UAAUA,CAAA;IACR,IAAI,CAAC7D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACuB,WAAW,CAACiC,KAAK,EAAE;IACxB,IAAI,CAACG,kBAAkB,EAAE;EAC3B;EAEA;EACAI,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACG,mBAAmB,CAACH,IAAI,CAAC;;EAElC;EAEAI,UAAUA,CAACL,KAAgB;IACzBA,KAAK,CAACM,cAAc,EAAE;IACtB,IAAI,CAAClE,QAAQ,GAAG,KAAK;IAErB,MAAM+D,KAAK,GAAGH,KAAK,CAACO,YAAY,EAAEJ,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACJ,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEtC;EAEAM,UAAUA,CAACT,KAAgB;IACzBA,KAAK,CAACM,cAAc,EAAE;IACtB,IAAI,CAAClE,QAAQ,GAAG,IAAI;EACtB;EAEAsE,WAAWA,CAACV,KAAgB;IAC1BA,KAAK,CAACM,cAAc,EAAE;IACtB,IAAI,CAAClE,QAAQ,GAAG,KAAK;EACvB;EAEAgE,mBAAmBA,CAACH,IAAU;IAC5B;IACA,MAAMU,aAAa,GAAG,GAAG,GAAGV,IAAI,CAACW,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEhC,WAAW,EAAE;IACrE,IAAI,CAAC,IAAI,CAACjC,iBAAiB,CAACkC,QAAQ,CAAC4B,aAAa,CAAC,EAAE;MACnD,IAAI,CAAClC,SAAS,CAAC,kDAAkD,GAAG,IAAI,CAAC5B,iBAAiB,CAACkE,IAAI,CAAC,IAAI,CAAC,CAAC;MACtG;;IAGF;IACA,IAAId,IAAI,CAACe,IAAI,GAAG,IAAI,CAAClE,WAAW,EAAE;MAChC,IAAI,CAAC2B,SAAS,CAAC,gDAAgD,CAAC;MAChE;;IAGF,IAAI,CAACvC,YAAY,GAAG+D,IAAI;IAExB;IACA,IAAIA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACnF,WAAW,GAAGmF,CAAC,CAACpB,MAAM,EAAEqB,MAAgB;MAC/C,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACvB,IAAI,CAAC;KAC3B,MAAM;MACL,IAAI,CAAC9D,WAAW,GAAG,IAAI;;EAE3B;EAEAsF,UAAUA,CAAA;IACR,IAAI,CAAC9B,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAACzD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EAEAsF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnE,WAAW,CAACoE,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,MAAMC,WAAW,GAAG,IAAI,CAACtE,WAAW,CAACjB,KAAK;IAE1C,IAAI,IAAI,CAACP,SAAS,IAAI,IAAI,CAACC,gBAAgB,EAAE;MAC3C,IAAI,CAAC8F,aAAa,CAAC,IAAI,CAAC9F,gBAAgB,EAAE6F,WAAW,CAAC;KACvD,MAAM;MACL,IAAI,CAACE,aAAa,CAACF,WAAW,CAAC;;EAEnC;EAEAE,aAAaA,CAACF,WAAgB;IAC5B,IAAI,CAAClG,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,IAAI,CAACO,YAAY,EAAE;MACrB,MAAM8F,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAACO,OAAO,CAACC,GAAG,IAAG;QACrCL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAER,WAAW,CAACQ,GAAG,CAAC,CAAC;MACxC,CAAC,CAAC;MACFL,QAAQ,CAACM,MAAM,CAAC,SAAS,EAAE,IAAI,CAACpG,YAAY,CAAC;MAE7C,IAAI,CAACf,mBAAmB,CAACoH,0BAA0B,CAACP,QAAQ,CAAC,CAAC5D,SAAS,CAAC;QACtEC,IAAI,EAAGO,OAAO,IAAI;UAChB,IAAI,CAACpD,QAAQ,CAACgH,IAAI,CAAC5D,OAAO,CAAC;UAC3B,IAAI,CAACD,YAAY,EAAE;UACnB,IAAI,CAAC8D,WAAW,CAAC,2BAA2B,CAAC;UAC7C,IAAI,CAAC3C,UAAU,EAAE;UACjB,IAAI,CAACnE,OAAO,GAAG,KAAK;QACtB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;UAC1D,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACR,mBAAmB,CAACuH,kBAAkB,CAACb,WAAW,CAAC,CAACzD,SAAS,CAAC;QACjEC,IAAI,EAAGO,OAAO,IAAI;UAChB,IAAI,CAACpD,QAAQ,CAACgH,IAAI,CAAC5D,OAAO,CAAC;UAC3B,IAAI,CAACD,YAAY,EAAE;UACnB,IAAI,CAAC8D,WAAW,CAAC,2BAA2B,CAAC;UAC7C,IAAI,CAAC3C,UAAU,EAAE;UACjB,IAAI,CAACnE,OAAO,GAAG,KAAK;QACtB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;UAC1D,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAmG,aAAaA,CAACjC,EAAU,EAAEgC,WAAgB;IACxC,IAAI,CAAClG,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,IAAI,CAACO,YAAY,EAAE;MACrB,MAAM8F,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAACO,OAAO,CAACC,GAAG,IAAG;QACrCL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAER,WAAW,CAACQ,GAAG,CAAC,CAAC;MACxC,CAAC,CAAC;MACFL,QAAQ,CAACM,MAAM,CAAC,SAAS,EAAE,IAAI,CAACpG,YAAY,CAAC;MAE7C,IAAI,CAACf,mBAAmB,CAACwH,0BAA0B,CAAC9C,EAAE,EAAEmC,QAAQ,CAAC,CAAC5D,SAAS,CAAC;QAC1EC,IAAI,EAAGuE,cAAc,IAAI;UACvB,MAAMC,KAAK,GAAG,IAAI,CAACrH,QAAQ,CAACsH,SAAS,CAACvE,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKA,EAAE,CAAC;UACvD,IAAIgD,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACrH,QAAQ,CAACqH,KAAK,CAAC,GAAGD,cAAc;YACrC,IAAI,CAACjE,YAAY,EAAE;;UAErB,IAAI,CAAC8D,WAAW,CAAC,8BAA8B,CAAC;UAChD,IAAI,CAAC3C,UAAU,EAAE;UACjB,IAAI,CAACnE,OAAO,GAAG,KAAK;QACtB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,8CAA8C,CAAC;UAC9D,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACR,mBAAmB,CAAC4H,kBAAkB,CAAClD,EAAE,EAAEgC,WAAW,CAAC,CAACzD,SAAS,CAAC;QACrEC,IAAI,EAAGuE,cAAc,IAAI;UACvB,MAAMC,KAAK,GAAG,IAAI,CAACrH,QAAQ,CAACsH,SAAS,CAACvE,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKA,EAAE,CAAC;UACvD,IAAIgD,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACrH,QAAQ,CAACqH,KAAK,CAAC,GAAGD,cAAc;YACrC,IAAI,CAACjE,YAAY,EAAE;;UAErB,IAAI,CAAC8D,WAAW,CAAC,8BAA8B,CAAC;UAChD,IAAI,CAAC3C,UAAU,EAAE;UACjB,IAAI,CAACnE,OAAO,GAAG,KAAK;QACtB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,8CAA8C,CAAC;UAC9D,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAqH,aAAaA,CAACpE,OAAqB;IACjC,IAAIqE,OAAO,CAAC,kDAAkDrE,OAAO,CAACnB,MAAM,KAAK,CAAC,EAAE;MAClF,IAAI,CAAC9B,OAAO,GAAG,IAAI;MACnB,IAAI,CAACR,mBAAmB,CAAC+H,kBAAkB,CAACtE,OAAO,CAACiB,EAAE,CAAC,CAACzB,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC7C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKjB,OAAO,CAACiB,EAAE,CAAC;UAC9D,IAAI,CAAClB,YAAY,EAAE;UACnB,IAAI,CAAC8D,WAAW,CAAC,+BAA+B,CAAC;UACjD,IAAI,CAAC9G,OAAO,GAAG,KAAK;QACtB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,6CAA6C,CAAC;UAC7D,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAwH,YAAYA,CAACvE,OAAqB;IAChC,IAAIA,OAAO,CAACwE,aAAa,EAAE;MACzB,IAAI,CAACjI,mBAAmB,CAACgI,YAAY,CAACvE,OAAO,CAACiB,EAAE,CAAC,CAACzB,SAAS,CAAC;QAC1DC,IAAI,EAAGgF,IAAI,IAAI;UACb,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAGlF,OAAO,CAACmF,kBAAkB,IAAI,aAAa;UAC3DL,IAAI,CAACM,KAAK,EAAE;UACZT,MAAM,CAACC,GAAG,CAACS,eAAe,CAACX,GAAG,CAAC;QACjC,CAAC;QACD9E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;QAC5D;OACD,CAAC;;EAEN;EAEAiB,qBAAqBA,CAAA;IACnB,MAAMwE,GAAG,GAAG,IAAInG,IAAI,EAAE;IACtB,MAAMoG,IAAI,GAAGD,GAAG,CAACE,WAAW,EAAE;IAC9B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,GAAG,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMC,GAAG,GAAGH,MAAM,CAACJ,GAAG,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,MAAMG,IAAI,GAAGL,MAAM,CAACJ,GAAG,CAACU,QAAQ,EAAE,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGF,MAAM,CAACJ,GAAG,CAACW,UAAU,EAAE,CAAC,CAACL,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEhG,MAAM/G,MAAM,GAAG,MAAM0G,IAAI,GAAGE,KAAK,GAAGI,GAAG,IAAIE,IAAI,EAAE;IACjD,IAAI,CAACpH,WAAW,CAACkC,UAAU,CAAC;MAAEhC;IAAM,CAAE,CAAC;EACzC;EAEAqH,cAAcA,CAAC7G,MAAqB;IAClC,MAAM8G,MAAM,GAAG,IAAI,CAAC1I,aAAa,CAAC2I,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3I,KAAK,KAAK2B,MAAM,CAAC;IACnE,OAAO8G,MAAM,GAAGA,MAAM,CAACvI,KAAK,GAAG,SAAS;EAC1C;EAEA0I,cAAcA,CAACjH,MAAqB;IAClC,QAAQA,MAAM;MACZ,KAAKjD,aAAa,CAACuB,SAAS;QAC1B,OAAO,kBAAkB;MAC3B,KAAKvB,aAAa,CAACyB,OAAO;QACxB,OAAO,gBAAgB;MACzB,KAAKzB,aAAa,CAAC0B,KAAK;QACtB,OAAO,cAAc;MACvB,KAAK1B,aAAa,CAAC2B,QAAQ;QACzB,OAAO,eAAe;MACxB,KAAK3B,aAAa,CAAC4B,OAAO;QACxB,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;;EAEf;EAEAuI,kBAAkBA,CAACxH,aAAqB;IACtC,MAAMqB,WAAW,GAAG,IAAI,CAACtD,YAAY,CAACsJ,IAAI,CAACzG,CAAC,IAAIA,CAAC,CAACsB,EAAE,KAAKlC,aAAa,CAAC;IACvE,OAAOqB,WAAW,GAAGA,WAAW,CAACC,GAAG,GAAG,qBAAqB;EAC9D;EAEAmG,OAAOA,CAACxG,OAAqB;IAC3B,OAAO,CAAC,EAAEA,OAAO,CAACwE,aAAa,IAAIxE,OAAO,CAACmF,kBAAkB,CAAC;EAChE;EAEAsB,WAAWA,CAACC,WAA+B;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIvE,IAAI,GAAGsE,WAAW;IACtB,IAAIE,SAAS,GAAG,CAAC;IAEjB,OAAOxE,IAAI,IAAI,IAAI,IAAIwE,SAAS,GAAGD,KAAK,CAAC/E,MAAM,GAAG,CAAC,EAAE;MACnDQ,IAAI,IAAI,IAAI;MACZwE,SAAS,EAAE;;IAGb,OAAO,GAAGxE,IAAI,CAACyE,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE;EACjD;EAEQ5D,oBAAoBA,CAAA;IAC1BM,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5E,WAAW,CAACmI,QAAQ,CAAC,CAACtD,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMsD,OAAO,GAAG,IAAI,CAACpI,WAAW,CAACqI,GAAG,CAACvD,GAAG,CAAC;MACzCsD,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQpD,WAAWA,CAACqD,OAAe;IACjC,IAAI,CAACvK,QAAQ,CAAC2B,IAAI,CAAC4I,OAAO,EAAE,QAAQ,EAAE;MACpC3I,QAAQ,EAAE,IAAI;MACd4I,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQtH,SAASA,CAACqH,OAAe;IAC/B,IAAI,CAACvK,QAAQ,CAAC2B,IAAI,CAAC4I,OAAO,EAAE,QAAQ,EAAE;MACpC3I,QAAQ,EAAE,IAAI;MACd4I,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAItI,MAAMA,CAAA;IAAK,OAAO,IAAI,CAACF,WAAW,CAACqI,GAAG,CAAC,QAAQ,CAAC;EAAE;EACtD,IAAIjI,aAAaA,CAAA;IAAK,OAAO,IAAI,CAACJ,WAAW,CAACqI,GAAG,CAAC,eAAe,CAAC;EAAE;EACpE,IAAIhI,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACL,WAAW,CAACqI,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAI9H,IAAIA,CAAA;IAAK,OAAO,IAAI,CAACP,WAAW,CAACqI,GAAG,CAAC,MAAM,CAAC;EAAE;EAClD,IAAI5H,YAAYA,CAAA;IAAK,OAAO,IAAI,CAACT,WAAW,CAACqI,GAAG,CAAC,cAAc,CAAC;EAAE;EAClE,IAAI3H,MAAMA,CAAA;IAAK,OAAO,IAAI,CAACV,WAAW,CAACqI,GAAG,CAAC,QAAQ,CAAC;EAAE;EACtD,IAAI1H,aAAaA,CAAA;IAAK,OAAO,IAAI,CAACX,WAAW,CAACqI,GAAG,CAAC,eAAe,CAAC;EAAE;;;uBA/bzD3K,sBAAsB,EAAA+K,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtB1L,sBAAsB;MAAA2L,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbnCjB,EAAA,CAAAmB,cAAA,QAAG;UAAAnB,EAAA,CAAAoB,MAAA,4BAAqB;UAAApB,EAAA,CAAAqB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}