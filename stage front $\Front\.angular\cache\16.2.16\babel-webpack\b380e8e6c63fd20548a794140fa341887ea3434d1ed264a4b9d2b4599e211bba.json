{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/societe.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nexport class ParametresSocietesComponent {\n  constructor(societeService, authService, formBuilder, snackBar) {\n    this.societeService = societeService;\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.societe = null;\n    this.loading = false;\n    this.saving = false;\n    // Gestion des fichiers\n    this.logoFile = null;\n    this.signatureFile = null;\n    this.cachetFile = null;\n    this.logoPreview = null;\n    this.signaturePreview = null;\n    this.cachetPreview = null;\n    // Types de fichiers acceptés pour les images\n    this.acceptedImageTypes = ['.jpg', '.jpeg', '.png', '.gif'];\n    this.maxImageSize = 2 * 1024 * 1024; // 2MB\n  }\n\n  ngOnInit() {\n    // Vérifier les permissions - seuls les admins peuvent modifier la société\n    if (!this.authService.canManageCompany()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', {\n        duration: 3000\n      });\n      return;\n    }\n    this.initializeForm();\n    this.loadSociete();\n  }\n  initializeForm() {\n    this.societeForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      adresse: ['', [Validators.required]],\n      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],\n      email: ['', [Validators.email]],\n      telephone: ['']\n    });\n  }\n  loadSociete() {\n    this.loading = true;\n    this.societeService.getSociete().subscribe({\n      next: societe => {\n        this.societe = societe;\n        this.societeForm.patchValue({\n          nom: societe.nom,\n          adresse: societe.adresse,\n          matriculeFiscale: societe.matriculeFiscale,\n          email: societe.email || '',\n          telephone: societe.telephone || ''\n        });\n        // Charger les aperçus des images existantes\n        if (societe.logo) {\n          this.logoPreview = societe.logo;\n        }\n        if (societe.signature) {\n          this.signaturePreview = societe.signature;\n        }\n        if (societe.cachet) {\n          this.cachetPreview = societe.cachet;\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des informations de la société');\n        this.loading = false;\n      }\n    });\n  }\n  // Gestion des fichiers\n  onLogoSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'logo');\n    }\n  }\n  onSignatureSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'signature');\n    }\n  }\n  onCachetSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'cachet');\n    }\n  }\n  handleImageSelection(file, type) {\n    // Vérifier le type de fichier\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.acceptedImageTypes.includes(fileExtension)) {\n      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedImageTypes.join(', '));\n      return;\n    }\n    // Vérifier la taille du fichier\n    if (file.size > this.maxImageSize) {\n      this.showError('Fichier trop volumineux. Taille maximale: 2MB');\n      return;\n    }\n    // Stocker le fichier selon le type\n    switch (type) {\n      case 'logo':\n        this.logoFile = file;\n        break;\n      case 'signature':\n        this.signatureFile = file;\n        break;\n      case 'cachet':\n        this.cachetFile = file;\n        break;\n    }\n    // Créer un aperçu\n    const reader = new FileReader();\n    reader.onload = e => {\n      const result = e.target?.result;\n      switch (type) {\n        case 'logo':\n          this.logoPreview = result;\n          break;\n        case 'signature':\n          this.signaturePreview = result;\n          break;\n        case 'cachet':\n          this.cachetPreview = result;\n          break;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  removeLogo() {\n    this.logoFile = null;\n    this.logoPreview = null;\n  }\n  removeSignature() {\n    this.signatureFile = null;\n    this.signaturePreview = null;\n  }\n  removeCachet() {\n    this.cachetFile = null;\n    this.cachetPreview = null;\n  }\n  onSubmit() {\n    if (this.societeForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.saving = true;\n    const formData = this.societeForm.value;\n    // Préparer les données avec les fichiers\n    const updateData = {\n      ...formData\n    };\n    // Convertir les fichiers en base64 si nécessaire\n    const promises = [];\n    if (this.logoFile) {\n      promises.push(this.convertFileToBase64(this.logoFile).then(base64 => {\n        updateData.logo = base64;\n      }));\n    }\n    if (this.signatureFile) {\n      promises.push(this.convertFileToBase64(this.signatureFile).then(base64 => {\n        updateData.signature = base64;\n      }));\n    }\n    if (this.cachetFile) {\n      promises.push(this.convertFileToBase64(this.cachetFile).then(base64 => {\n        updateData.cachet = base64;\n      }));\n    }\n    Promise.all(promises).then(() => {\n      this.societeService.updateSociete(updateData).subscribe({\n        next: () => {\n          this.showSuccess('Informations de la société mises à jour avec succès');\n          this.loadSociete(); // Recharger pour obtenir les données mises à jour\n          this.saving = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la mise à jour des informations');\n          this.saving = false;\n        }\n      });\n    });\n  }\n  convertFileToBase64(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const result = reader.result;\n        resolve(result);\n      };\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.societeForm.controls).forEach(key => {\n      const control = this.societeForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() {\n    return this.societeForm.get('nom');\n  }\n  get adresse() {\n    return this.societeForm.get('adresse');\n  }\n  get matriculeFiscale() {\n    return this.societeForm.get('matriculeFiscale');\n  }\n  get email() {\n    return this.societeForm.get('email');\n  }\n  get telephone() {\n    return this.societeForm.get('telephone');\n  }\n  static {\n    this.ɵfac = function ParametresSocietesComponent_Factory(t) {\n      return new (t || ParametresSocietesComponent)(i0.ɵɵdirectiveInject(i1.SocieteService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ParametresSocietesComponent,\n      selectors: [[\"app-parametres-societes\"]],\n      decls: 2,\n      vars: 0,\n      template: function ParametresSocietesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"parametres-societes works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "ParametresSocietesComponent", "constructor", "societeService", "authService", "formBuilder", "snackBar", "societe", "loading", "saving", "logoFile", "signatureFile", "cachetFile", "logoPreview", "signaturePreview", "cachetPreview", "acceptedImageTypes", "maxImageSize", "ngOnInit", "canManageCompany", "open", "duration", "initializeForm", "loadSociete", "societeForm", "group", "nom", "required", "<PERSON><PERSON><PERSON><PERSON>", "adresse", "matriculeFiscale", "email", "telephone", "getSociete", "subscribe", "next", "patchValue", "logo", "signature", "cachet", "error", "showError", "onLogoSelected", "event", "file", "target", "files", "handleImageSelection", "onSignatureSelected", "onCachetSelected", "type", "fileExtension", "name", "split", "pop", "toLowerCase", "includes", "join", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeLogo", "removeSignature", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "invalid", "markFormGroupTouched", "formData", "value", "updateData", "promises", "push", "convertFileToBase64", "then", "base64", "Promise", "all", "updateSociete", "showSuccess", "resolve", "reject", "onerror", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "panelClass", "i0", "ɵɵdirectiveInject", "i1", "SocieteService", "i2", "AuthService", "i3", "FormBuilder", "i4", "MatSnackBar", "selectors", "decls", "vars", "template", "ParametresSocietesComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\parametres-societes\\parametres-societes.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\parametres-societes\\parametres-societes.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SocieteService } from '../../services/societe.service';\nimport { AuthService } from '../../services/auth.service';\nimport { Societe } from '../models';\n\n@Component({\n  selector: 'app-parametres-societes',\n  templateUrl: './parametres-societes.component.html',\n  styleUrls: ['./parametres-societes.component.css']\n})\nexport class ParametresSocietesComponent implements OnInit {\n  societeForm!: FormGroup;\n  societe: Societe | null = null;\n  loading = false;\n  saving = false;\n\n  // Gestion des fichiers\n  logoFile: File | null = null;\n  signatureFile: File | null = null;\n  cachetFile: File | null = null;\n\n  logoPreview: string | null = null;\n  signaturePreview: string | null = null;\n  cachetPreview: string | null = null;\n\n  // Types de fichiers acceptés pour les images\n  acceptedImageTypes = ['.jpg', '.jpeg', '.png', '.gif'];\n  maxImageSize = 2 * 1024 * 1024; // 2MB\n\n  constructor(\n    private societeService: SocieteService,\n    private authService: AuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Vérifier les permissions - seuls les admins peuvent modifier la société\n    if (!this.authService.canManageCompany()) {\n      this.snackBar.open('Accès non autorisé - Réservé aux administrateurs', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.initializeForm();\n    this.loadSociete();\n  }\n\n  initializeForm(): void {\n    this.societeForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      adresse: ['', [Validators.required]],\n      matriculeFiscale: ['', [Validators.required, Validators.minLength(8)]],\n      email: ['', [Validators.email]],\n      telephone: ['']\n    });\n  }\n\n  loadSociete(): void {\n    this.loading = true;\n    this.societeService.getSociete().subscribe({\n      next: (societe) => {\n        this.societe = societe;\n        this.societeForm.patchValue({\n          nom: societe.nom,\n          adresse: societe.adresse,\n          matriculeFiscale: societe.matriculeFiscale,\n          email: societe.email || '',\n          telephone: societe.telephone || ''\n        });\n\n        // Charger les aperçus des images existantes\n        if (societe.logo) {\n          this.logoPreview = societe.logo;\n        }\n        if (societe.signature) {\n          this.signaturePreview = societe.signature;\n        }\n        if (societe.cachet) {\n          this.cachetPreview = societe.cachet;\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors du chargement des informations de la société');\n        this.loading = false;\n      }\n    });\n  }\n\n  // Gestion des fichiers\n  onLogoSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'logo');\n    }\n  }\n\n  onSignatureSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'signature');\n    }\n  }\n\n  onCachetSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.handleImageSelection(file, 'cachet');\n    }\n  }\n\n  handleImageSelection(file: File, type: 'logo' | 'signature' | 'cachet'): void {\n    // Vérifier le type de fichier\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.acceptedImageTypes.includes(fileExtension)) {\n      this.showError('Type de fichier non autorisé. Formats acceptés: ' + this.acceptedImageTypes.join(', '));\n      return;\n    }\n\n    // Vérifier la taille du fichier\n    if (file.size > this.maxImageSize) {\n      this.showError('Fichier trop volumineux. Taille maximale: 2MB');\n      return;\n    }\n\n    // Stocker le fichier selon le type\n    switch (type) {\n      case 'logo':\n        this.logoFile = file;\n        break;\n      case 'signature':\n        this.signatureFile = file;\n        break;\n      case 'cachet':\n        this.cachetFile = file;\n        break;\n    }\n\n    // Créer un aperçu\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const result = e.target?.result as string;\n      switch (type) {\n        case 'logo':\n          this.logoPreview = result;\n          break;\n        case 'signature':\n          this.signaturePreview = result;\n          break;\n        case 'cachet':\n          this.cachetPreview = result;\n          break;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeLogo(): void {\n    this.logoFile = null;\n    this.logoPreview = null;\n  }\n\n  removeSignature(): void {\n    this.signatureFile = null;\n    this.signaturePreview = null;\n  }\n\n  removeCachet(): void {\n    this.cachetFile = null;\n    this.cachetPreview = null;\n  }\n\n  onSubmit(): void {\n    if (this.societeForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.saving = true;\n    const formData = this.societeForm.value;\n\n    // Préparer les données avec les fichiers\n    const updateData: any = { ...formData };\n\n    // Convertir les fichiers en base64 si nécessaire\n    const promises: Promise<void>[] = [];\n\n    if (this.logoFile) {\n      promises.push(this.convertFileToBase64(this.logoFile).then(base64 => {\n        updateData.logo = base64;\n      }));\n    }\n\n    if (this.signatureFile) {\n      promises.push(this.convertFileToBase64(this.signatureFile).then(base64 => {\n        updateData.signature = base64;\n      }));\n    }\n\n    if (this.cachetFile) {\n      promises.push(this.convertFileToBase64(this.cachetFile).then(base64 => {\n        updateData.cachet = base64;\n      }));\n    }\n\n    Promise.all(promises).then(() => {\n      this.societeService.updateSociete(updateData).subscribe({\n        next: () => {\n          this.showSuccess('Informations de la société mises à jour avec succès');\n          this.loadSociete(); // Recharger pour obtenir les données mises à jour\n          this.saving = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la mise à jour des informations');\n          this.saving = false;\n        }\n      });\n    });\n  }\n\n  private convertFileToBase64(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const result = reader.result as string;\n        resolve(result);\n      };\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.societeForm.controls).forEach(key => {\n      const control = this.societeForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() { return this.societeForm.get('nom'); }\n  get adresse() { return this.societeForm.get('adresse'); }\n  get matriculeFiscale() { return this.societeForm.get('matriculeFiscale'); }\n  get email() { return this.societeForm.get('email'); }\n  get telephone() { return this.societeForm.get('telephone'); }\n}\n", "<p>parametres-societes works!</p>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;AAWnE,OAAM,MAAOC,2BAA2B;EAmBtCC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB;IAHrB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IArBlB,KAAAC,OAAO,GAAmB,IAAI;IAC9B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,MAAM,GAAG,KAAK;IAEd;IACA,KAAAC,QAAQ,GAAgB,IAAI;IAC5B,KAAAC,aAAa,GAAgB,IAAI;IACjC,KAAAC,UAAU,GAAgB,IAAI;IAE9B,KAAAC,WAAW,GAAkB,IAAI;IACjC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAAkB,IAAI;IAEnC;IACA,KAAAC,kBAAkB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IACtD,KAAAC,YAAY,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;EAO7B;;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACd,WAAW,CAACe,gBAAgB,EAAE,EAAE;MACxC,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,kDAAkD,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpG;;IAGF,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACE,WAAW,GAAG,IAAI,CAACnB,WAAW,CAACoB,KAAK,CAAC;MACxCC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACpCG,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACtEG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC/B,UAAU,CAAC+B,KAAK,CAAC,CAAC;MAC/BC,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;EACJ;EAEAT,WAAWA,CAAA;IACT,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,cAAc,CAAC8B,UAAU,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAG5B,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACiB,WAAW,CAACY,UAAU,CAAC;UAC1BV,GAAG,EAAEnB,OAAO,CAACmB,GAAG;UAChBG,OAAO,EAAEtB,OAAO,CAACsB,OAAO;UACxBC,gBAAgB,EAAEvB,OAAO,CAACuB,gBAAgB;UAC1CC,KAAK,EAAExB,OAAO,CAACwB,KAAK,IAAI,EAAE;UAC1BC,SAAS,EAAEzB,OAAO,CAACyB,SAAS,IAAI;SACjC,CAAC;QAEF;QACA,IAAIzB,OAAO,CAAC8B,IAAI,EAAE;UAChB,IAAI,CAACxB,WAAW,GAAGN,OAAO,CAAC8B,IAAI;;QAEjC,IAAI9B,OAAO,CAAC+B,SAAS,EAAE;UACrB,IAAI,CAACxB,gBAAgB,GAAGP,OAAO,CAAC+B,SAAS;;QAE3C,IAAI/B,OAAO,CAACgC,MAAM,EAAE;UAClB,IAAI,CAACxB,aAAa,GAAGR,OAAO,CAACgC,MAAM;;QAGrC,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,0DAA0D,CAAC;QAC1E,IAAI,CAACjC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAkC,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACG,oBAAoB,CAACH,IAAI,EAAE,MAAM,CAAC;;EAE3C;EAEAI,mBAAmBA,CAACL,KAAU;IAC5B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACG,oBAAoB,CAACH,IAAI,EAAE,WAAW,CAAC;;EAEhD;EAEAK,gBAAgBA,CAACN,KAAU;IACzB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACG,oBAAoB,CAACH,IAAI,EAAE,QAAQ,CAAC;;EAE7C;EAEAG,oBAAoBA,CAACH,IAAU,EAAEM,IAAqC;IACpE;IACA,MAAMC,aAAa,GAAG,GAAG,GAAGP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;IACrE,IAAI,CAAC,IAAI,CAACvC,kBAAkB,CAACwC,QAAQ,CAACL,aAAa,CAAC,EAAE;MACpD,IAAI,CAACV,SAAS,CAAC,kDAAkD,GAAG,IAAI,CAACzB,kBAAkB,CAACyC,IAAI,CAAC,IAAI,CAAC,CAAC;MACvG;;IAGF;IACA,IAAIb,IAAI,CAACc,IAAI,GAAG,IAAI,CAACzC,YAAY,EAAE;MACjC,IAAI,CAACwB,SAAS,CAAC,+CAA+C,CAAC;MAC/D;;IAGF;IACA,QAAQS,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACxC,QAAQ,GAAGkC,IAAI;QACpB;MACF,KAAK,WAAW;QACd,IAAI,CAACjC,aAAa,GAAGiC,IAAI;QACzB;MACF,KAAK,QAAQ;QACX,IAAI,CAAChC,UAAU,GAAGgC,IAAI;QACtB;;IAGJ;IACA,MAAMe,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;MACpB,MAAMC,MAAM,GAAGD,CAAC,CAACjB,MAAM,EAAEkB,MAAgB;MACzC,QAAQb,IAAI;QACV,KAAK,MAAM;UACT,IAAI,CAACrC,WAAW,GAAGkD,MAAM;UACzB;QACF,KAAK,WAAW;UACd,IAAI,CAACjD,gBAAgB,GAAGiD,MAAM;UAC9B;QACF,KAAK,QAAQ;UACX,IAAI,CAAChD,aAAa,GAAGgD,MAAM;UAC3B;;IAEN,CAAC;IACDJ,MAAM,CAACK,aAAa,CAACpB,IAAI,CAAC;EAC5B;EAEAqB,UAAUA,CAAA;IACR,IAAI,CAACvD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,WAAW,GAAG,IAAI;EACzB;EAEAqD,eAAeA,CAAA;IACb,IAAI,CAACvD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACG,gBAAgB,GAAG,IAAI;EAC9B;EAEAqD,YAAYA,CAAA;IACV,IAAI,CAACvD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACG,aAAa,GAAG,IAAI;EAC3B;EAEAqD,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5C,WAAW,CAAC6C,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC7D,MAAM,GAAG,IAAI;IAClB,MAAM8D,QAAQ,GAAG,IAAI,CAAC/C,WAAW,CAACgD,KAAK;IAEvC;IACA,MAAMC,UAAU,GAAQ;MAAE,GAAGF;IAAQ,CAAE;IAEvC;IACA,MAAMG,QAAQ,GAAoB,EAAE;IAEpC,IAAI,IAAI,CAAChE,QAAQ,EAAE;MACjBgE,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAClE,QAAQ,CAAC,CAACmE,IAAI,CAACC,MAAM,IAAG;QAClEL,UAAU,CAACpC,IAAI,GAAGyC,MAAM;MAC1B,CAAC,CAAC,CAAC;;IAGL,IAAI,IAAI,CAACnE,aAAa,EAAE;MACtB+D,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACjE,aAAa,CAAC,CAACkE,IAAI,CAACC,MAAM,IAAG;QACvEL,UAAU,CAACnC,SAAS,GAAGwC,MAAM;MAC/B,CAAC,CAAC,CAAC;;IAGL,IAAI,IAAI,CAAClE,UAAU,EAAE;MACnB8D,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAChE,UAAU,CAAC,CAACiE,IAAI,CAACC,MAAM,IAAG;QACpEL,UAAU,CAAClC,MAAM,GAAGuC,MAAM;MAC5B,CAAC,CAAC,CAAC;;IAGLC,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC,CAACG,IAAI,CAAC,MAAK;MAC9B,IAAI,CAAC1E,cAAc,CAAC8E,aAAa,CAACR,UAAU,CAAC,CAACvC,SAAS,CAAC;QACtDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC+C,WAAW,CAAC,qDAAqD,CAAC;UACvE,IAAI,CAAC3D,WAAW,EAAE,CAAC,CAAC;UACpB,IAAI,CAACd,MAAM,GAAG,KAAK;QACrB,CAAC;QACD+B,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,gDAAgD,CAAC;UAChE,IAAI,CAAChC,MAAM,GAAG,KAAK;QACrB;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQmE,mBAAmBA,CAAChC,IAAU;IACpC,OAAO,IAAImC,OAAO,CAAC,CAACI,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMzB,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAME,MAAM,GAAGJ,MAAM,CAACI,MAAgB;QACtCoB,OAAO,CAACpB,MAAM,CAAC;MACjB,CAAC;MACDJ,MAAM,CAAC0B,OAAO,GAAGD,MAAM;MACvBzB,MAAM,CAACK,aAAa,CAACpB,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEQ0B,oBAAoBA,CAAA;IAC1BgB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,WAAW,CAACgE,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAACnE,WAAW,CAACoE,GAAG,CAACF,GAAG,CAAC;MACzCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQX,WAAWA,CAACY,OAAe;IACjC,IAAI,CAACxF,QAAQ,CAACc,IAAI,CAAC0E,OAAO,EAAE,QAAQ,EAAE;MACpCzE,QAAQ,EAAE,IAAI;MACd0E,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQtD,SAASA,CAACqD,OAAe;IAC/B,IAAI,CAACxF,QAAQ,CAACc,IAAI,CAAC0E,OAAO,EAAE,QAAQ,EAAE;MACpCzE,QAAQ,EAAE,IAAI;MACd0E,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAIrE,GAAGA,CAAA;IAAK,OAAO,IAAI,CAACF,WAAW,CAACoE,GAAG,CAAC,KAAK,CAAC;EAAE;EAChD,IAAI/D,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACL,WAAW,CAACoE,GAAG,CAAC,SAAS,CAAC;EAAE;EACxD,IAAI9D,gBAAgBA,CAAA;IAAK,OAAO,IAAI,CAACN,WAAW,CAACoE,GAAG,CAAC,kBAAkB,CAAC;EAAE;EAC1E,IAAI7D,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACP,WAAW,CAACoE,GAAG,CAAC,OAAO,CAAC;EAAE;EACpD,IAAI5D,SAASA,CAAA;IAAK,OAAO,IAAI,CAACR,WAAW,CAACoE,GAAG,CAAC,WAAW,CAAC;EAAE;;;uBAzPjD3F,2BAA2B,EAAA+F,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA3BxG,2BAA2B;MAAAyG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZxCf,EAAA,CAAAiB,cAAA,QAAG;UAAAjB,EAAA,CAAAkB,MAAA,iCAA0B;UAAAlB,EAAA,CAAAmB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}