{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/card\";\nfunction LoginComponent_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" L'email est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format d'email invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le mot de passe doit contenir au moins 6 caract\\u00E8res \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.error);\n  }\n}\nfunction LoginComponent_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.redirectBasedOnRole();\n      return;\n    }\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    const {\n      email,\n      motDePasse\n    } = this.loginForm.value;\n    this.authService.login(email, motDePasse).subscribe({\n      next: response => {\n        this.loading = false;\n        this.redirectBasedOnRole();\n      },\n      error: error => {\n        this.loading = false;\n        this.error = error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.';\n      }\n    });\n  }\n  redirectBasedOnRole() {\n    if (this.authService.isAdmin()) {\n      this.router.navigate(['/admin-dashboard']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get email() {\n    return this.loginForm.get('email');\n  }\n  get motDePasse() {\n    return this.loginForm.get('motDePasse');\n  }\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 36,\n      vars: 14,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"motDePasse\", \"placeholder\", \"Votre mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [\"routerLink\", \"/register\", 1, \"register-link\"], [1, \"error-message\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"Connectez-vous \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(9, \"mat-form-field\", 3)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 4);\n          i0.ɵɵelementStart(13, \"mat-icon\", 5);\n          i0.ɵɵtext(14, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, LoginComponent_mat_error_15_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(16, LoginComponent_mat_error_16_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-form-field\", 3)(18, \"mat-label\");\n          i0.ɵɵtext(19, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 7);\n          i0.ɵɵelementStart(21, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_21_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementStart(22, \"mat-icon\");\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, LoginComponent_mat_error_24_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(25, LoginComponent_mat_error_25_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, LoginComponent_div_26_Template, 5, 1, \"div\", 9);\n          i0.ɵɵelementStart(27, \"button\", 10);\n          i0.ɵɵtemplate(28, LoginComponent_mat_icon_28_Template, 2, 0, \"mat-icon\", 6);\n          i0.ɵɵtemplate(29, LoginComponent_span_29_Template, 2, 0, \"span\", 6);\n          i0.ɵɵtemplate(30, LoginComponent_span_30_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-card-actions\")(32, \"p\");\n          i0.ɵɵtext(33, \"Pas encore de compte ? \");\n          i0.ɵɵelementStart(34, \"a\", 11);\n          i0.ɵɵtext(35, \"S'inscrire\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.email == null ? null : ctx.email.hasError(\"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.motDePasse == null ? null : ctx.motDePasse.hasError(\"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: 20px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border-radius: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  margin-top: 16px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #f44336;\\n  margin-bottom: 16px;\\n  padding: 8px;\\n  background-color: #ffebee;\\n  border-radius: 4px;\\n  border-left: 4px solid #f44336;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\nmat-card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\nmat-card-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\nmat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r4", "error", "LoginComponent", "constructor", "formBuilder", "authService", "router", "loading", "hidePassword", "ngOnInit", "isAuthenticated", "redirectBasedOnRole", "loginForm", "group", "email", "required", "motDePasse", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "invalid", "markFormGroupTouched", "value", "login", "subscribe", "next", "response", "message", "isAdmin", "navigate", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "togglePasswordVisibility", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_8_listener", "ɵɵelement", "ɵɵtemplate", "LoginComponent_mat_error_15_Template", "LoginComponent_mat_error_16_Template", "LoginComponent_Template_button_click_21_listener", "LoginComponent_mat_error_24_Template", "LoginComponent_mat_error_25_Template", "LoginComponent_div_26_Template", "LoginComponent_mat_icon_28_Template", "LoginComponent_span_29_Template", "LoginComponent_span_30_Template", "ɵɵproperty", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵattribute"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\login\\login.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup;\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.redirectBasedOnRole();\n      return;\n    }\n\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      motDePasse: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    const { email, motDePasse } = this.loginForm.value;\n\n    this.authService.login(email, motDePasse).subscribe({\n      next: (response) => {\n        this.loading = false;\n        this.redirectBasedOnRole();\n      },\n      error: (error) => {\n        this.loading = false;\n        this.error = error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.';\n      }\n    });\n  }\n\n  private redirectBasedOnRole(): void {\n    if (this.authService.isAdmin()) {\n      this.router.navigate(['/admin-dashboard']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get email() { return this.loginForm.get('email'); }\n  get motDePasse() { return this.loginForm.get('motDePasse'); }\n\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n}\n", "<div class=\"login-container\">\n  <mat-card class=\"login-card\">\n    <mat-card-header>\n      <mat-card-title>Connexion</mat-card-title>\n      <mat-card-subtitle>Connectez-vous à votre compte</mat-card-subtitle>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n        <!-- Champ Email -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Email</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error *ngIf=\"email?.hasError('required')\">\n            L'email est requis\n          </mat-error>\n          <mat-error *ngIf=\"email?.hasError('email')\">\n            Format d'email invalide\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Champ Mot de passe -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"motDePasse\" placeholder=\"Votre mot de passe\">\n          <button mat-icon-button matSuffix (click)=\"togglePasswordVisibility()\"\n                  type=\"button\" [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\">\n            <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n          </button>\n          <mat-error *ngIf=\"motDePasse?.hasError('required')\">\n            Le mot de passe est requis\n          </mat-error>\n          <mat-error *ngIf=\"motDePasse?.hasError('minlength')\">\n            Le mot de passe doit contenir au moins 6 caractères\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Message d'erreur -->\n        <div *ngIf=\"error\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ error }}</span>\n        </div>\n\n        <!-- Bouton de connexion -->\n        <button mat-raised-button color=\"primary\" type=\"submit\"\n                class=\"full-width login-button\" [disabled]=\"loading\">\n          <mat-icon *ngIf=\"loading\">hourglass_empty</mat-icon>\n          <span *ngIf=\"!loading\">Se connecter</span>\n          <span *ngIf=\"loading\">Connexion en cours...</span>\n        </button>\n      </form>\n    </mat-card-content>\n\n    <mat-card-actions>\n      <p>Pas encore de compte ?\n        <a routerLink=\"/register\" class=\"register-link\">S'inscrire</a>\n      </p>\n    </mat-card-actions>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;ICazDC,EAAA,CAAAC,cAAA,gBAA+C;IAC7CD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA4C;IAC1CD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAaZH,EAAA,CAAAC,cAAA,gBAAoD;IAClDD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAqD;IACnDD,EAAA,CAAAE,MAAA,iEACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAIdH,EAAA,CAAAC,cAAA,cAAyC;IAC7BD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAMjBP,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACpDH,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1CH,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADzC5D,OAAM,MAAOK,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAN,KAAK,GAAG,EAAE;IACV,KAAAO,YAAY,GAAG,IAAI;EAMhB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACJ,WAAW,CAACK,eAAe,EAAE,EAAE;MACtC,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACqB,KAAK,CAAC,CAAC;MACpDE,UAAU,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC;KAChE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,SAAS,CAACO,OAAO,EAAE;MAC1B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,KAAK,GAAG,EAAE;IAEf,MAAM;MAAEa,KAAK;MAAEE;IAAU,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACS,KAAK;IAElD,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAACR,KAAK,EAAEE,UAAU,CAAC,CAACO,SAAS,CAAC;MAClDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACI,mBAAmB,EAAE;MAC5B,CAAC;MACDV,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACM,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,KAAK,GAAGA,KAAK,CAACA,KAAK,EAAEyB,OAAO,IAAI,iDAAiD;MACxF;KACD,CAAC;EACJ;EAEQf,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACN,WAAW,CAACsB,OAAO,EAAE,EAAE;MAC9B,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;KAC3C,MAAM;MACL,IAAI,CAACtB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEQR,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,SAAS,CAACmB,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACtB,SAAS,CAACuB,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;EACA,IAAItB,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACF,SAAS,CAACuB,GAAG,CAAC,OAAO,CAAC;EAAE;EAClD,IAAInB,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACJ,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;EAAE;EAE5DE,wBAAwBA,CAAA;IACtB,IAAI,CAAC7B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;;;uBArEWN,cAAc,EAAAR,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA4C,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd1C,cAAc;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV3BzD,EAAA,CAAAC,cAAA,aAA6B;UAGPD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC1CH,EAAA,CAAAC,cAAA,wBAAmB;UAAAD,EAAA,CAAAE,MAAA,yCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAoB;UAGtEH,EAAA,CAAAC,cAAA,uBAAkB;UACcD,EAAA,CAAA2D,UAAA,sBAAAC,iDAAA;YAAA,OAAYF,GAAA,CAAAlC,QAAA,EAAU;UAAA,EAAC;UAEnDxB,EAAA,CAAAC,cAAA,wBAAwD;UAC3CD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAA6D,SAAA,gBAAmF;UACnF7D,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA8D,UAAA,KAAAC,oCAAA,uBAEY;UACZ/D,EAAA,CAAA8D,UAAA,KAAAE,oCAAA,uBAEY;UACdhE,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UAC3CD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAA6D,SAAA,gBACqE;UACrE7D,EAAA,CAAAC,cAAA,iBAE2C;UAFTD,EAAA,CAAA2D,UAAA,mBAAAM,iDAAA;YAAA,OAASP,GAAA,CAAAf,wBAAA,EAA0B;UAAA,EAAC;UAGpE3C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEzEH,EAAA,CAAA8D,UAAA,KAAAI,oCAAA,uBAEY;UACZlE,EAAA,CAAA8D,UAAA,KAAAK,oCAAA,uBAEY;UACdnE,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAA8D,UAAA,KAAAM,8BAAA,iBAGM;UAGNpE,EAAA,CAAAC,cAAA,kBAC6D;UAC3DD,EAAA,CAAA8D,UAAA,KAAAO,mCAAA,sBAAoD;UACpDrE,EAAA,CAAA8D,UAAA,KAAAQ,+BAAA,kBAA0C;UAC1CtE,EAAA,CAAA8D,UAAA,KAAAS,+BAAA,kBAAkD;UACpDvE,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,wBAAkB;UACbD,EAAA,CAAAE,MAAA,+BACD;UAAAF,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAlD1DH,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwE,UAAA,cAAAd,GAAA,CAAAxC,SAAA,CAAuB;UAMblB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAAtC,KAAA,kBAAAsC,GAAA,CAAAtC,KAAA,CAAAqD,QAAA,aAAiC;UAGjCzE,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAAtC,KAAA,kBAAAsC,GAAA,CAAAtC,KAAA,CAAAqD,QAAA,UAA8B;UAQ1BzE,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAA5C,YAAA,uBAA2C;UAGrCd,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAA0E,WAAA,+BAAmC,iBAAAhB,GAAA,CAAA5C,YAAA;UAE7Cd,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAK,iBAAA,CAAAqD,GAAA,CAAA5C,YAAA,mCAAkD;UAElDd,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAApC,UAAA,kBAAAoC,GAAA,CAAApC,UAAA,CAAAmD,QAAA,aAAsC;UAGtCzE,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAApC,UAAA,kBAAAoC,GAAA,CAAApC,UAAA,CAAAmD,QAAA,cAAuC;UAM/CzE,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAAnD,KAAA,CAAW;UAOuBP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAwE,UAAA,aAAAd,GAAA,CAAA7C,OAAA,CAAoB;UAC/Cb,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAA7C,OAAA,CAAa;UACjBb,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAwE,UAAA,UAAAd,GAAA,CAAA7C,OAAA,CAAc;UACdb,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAwE,UAAA,SAAAd,GAAA,CAAA7C,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}