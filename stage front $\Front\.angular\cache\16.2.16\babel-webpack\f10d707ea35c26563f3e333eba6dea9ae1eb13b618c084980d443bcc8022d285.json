{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { StatutClient } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/client.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/material/snack-bar\";\nfunction ClientsComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ClientsComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.showAddForm());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Nouveau Client \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r8.label, \" \");\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le nom doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Format d'email invalide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"L'adresse est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r19.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r19.label, \" \");\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_error_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Le statut est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_mat_icon_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_mat_card_25_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.isEditing ? \"Modifier\" : \"Cr\\u00E9er\");\n  }\n}\nfunction ClientsComponent_mat_card_25_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.isEditing ? \"Modification...\" : \"Cr\\u00E9ation...\");\n  }\n}\nfunction ClientsComponent_mat_card_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 19)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"form\", 20);\n    i0.ɵɵlistener(\"ngSubmit\", function ClientsComponent_mat_card_25_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSubmit());\n    });\n    i0.ɵɵelementStart(8, \"div\", 21)(9, \"mat-form-field\", 22)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Nom complet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 23);\n    i0.ɵɵelementStart(13, \"mat-icon\", 7);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ClientsComponent_mat_card_25_mat_error_15_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵtemplate(16, ClientsComponent_mat_card_25_mat_error_16_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"mat-form-field\", 25)(19, \"mat-label\");\n    i0.ɵɵtext(20, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 26);\n    i0.ɵɵelementStart(22, \"mat-icon\", 7);\n    i0.ɵɵtext(23, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ClientsComponent_mat_card_25_mat_error_24_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵtemplate(25, ClientsComponent_mat_card_25_mat_error_25_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 25)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 27);\n    i0.ɵɵelementStart(30, \"mat-icon\", 7);\n    i0.ɵɵtext(31, \"phone\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 21)(33, \"mat-form-field\", 22)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"textarea\", 28);\n    i0.ɵɵelementStart(37, \"mat-icon\", 7);\n    i0.ɵɵtext(38, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, ClientsComponent_mat_card_25_mat_error_39_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 21)(41, \"mat-form-field\", 25)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-select\", 29);\n    i0.ɵɵtemplate(45, ClientsComponent_mat_card_25_mat_option_45_Template, 2, 2, \"mat-option\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(46, ClientsComponent_mat_card_25_mat_error_46_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 30)(48, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ClientsComponent_mat_card_25_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.cancelForm());\n    });\n    i0.ɵɵtext(49, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"button\", 32);\n    i0.ɵɵtemplate(51, ClientsComponent_mat_card_25_mat_icon_51_Template, 2, 0, \"mat-icon\", 24);\n    i0.ɵɵtemplate(52, ClientsComponent_mat_card_25_span_52_Template, 2, 1, \"span\", 24);\n    i0.ɵɵtemplate(53, ClientsComponent_mat_card_25_span_53_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.isEditing ? \"edit\" : \"add\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditing ? \"Modifier le client\" : \"Nouveau client\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.clientForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nom == null ? null : ctx_r2.nom.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nom == null ? null : ctx_r2.nom.hasError(\"minlength\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.email == null ? null : ctx_r2.email.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.email == null ? null : ctx_r2.email.hasError(\"email\"));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.adresse == null ? null : ctx_r2.adresse.hasError(\"required\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statutOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.statut == null ? null : ctx_r2.statut.hasError(\"required\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n  }\n}\nfunction ClientsComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"mat-spinner\", 34);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des clients...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientsComponent_div_32_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ClientsComponent_div_32_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.showAddForm());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Ajouter le premier client \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"people_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Aucun client trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ClientsComponent_div_32_button_5_Template, 4, 0, \"button\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.authService.canManageClients());\n  }\n}\nfunction ClientsComponent_table_33_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48)(1, \"div\", 49)(2, \"mat-icon\", 50);\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const client_r40 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(client_r40.nom);\n  }\n}\nfunction ClientsComponent_table_33_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48)(1, \"a\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r41 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", client_r41.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(client_r41.email);\n  }\n}\nfunction ClientsComponent_table_33_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_9_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r42 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(client_r42.telephone);\n  }\n}\nfunction ClientsComponent_table_33_td_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1, \"Non renseign\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48);\n    i0.ɵɵtemplate(1, ClientsComponent_table_33_td_9_span_1_Template, 2, 1, \"span\", 53);\n    i0.ɵɵtemplate(2, ClientsComponent_table_33_td_9_ng_template_2_Template, 2, 0, \"ng-template\", null, 54, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r42 = ctx.$implicit;\n    const _r44 = i0.ɵɵreference(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", client_r42.telephone)(\"ngIfElse\", _r44);\n  }\n}\nfunction ClientsComponent_table_33_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48)(1, \"span\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r47 = ctx.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r33.getStatutClass(client_r47.statut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getStatutLabel(client_r47.statut), \" \");\n  }\n}\nfunction ClientsComponent_table_33_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Date de cr\\u00E9ation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r48 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, client_r48.dateCreation, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction ClientsComponent_table_33_th_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientsComponent_table_33_td_18_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ClientsComponent_table_33_td_18_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const client_r49 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.editClient(client_r49));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientsComponent_table_33_td_18_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ClientsComponent_table_33_td_18_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const client_r49 = i0.ɵɵnextContext().$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.deleteClient(client_r49));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ClientsComponent_table_33_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 48);\n    i0.ɵɵtemplate(1, ClientsComponent_table_33_td_18_button_1_Template, 3, 0, \"button\", 57);\n    i0.ɵɵtemplate(2, ClientsComponent_table_33_td_18_button_2_Template, 3, 0, \"button\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.authService.canManageClients());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.authService.canManageClients());\n  }\n}\nfunction ClientsComponent_table_33_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 61);\n  }\n}\nfunction ClientsComponent_table_33_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 62);\n  }\n}\nfunction ClientsComponent_table_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 36);\n    i0.ɵɵelementContainerStart(1, 37);\n    i0.ɵɵtemplate(2, ClientsComponent_table_33_th_2_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(3, ClientsComponent_table_33_td_3_Template, 6, 1, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(4, 40);\n    i0.ɵɵtemplate(5, ClientsComponent_table_33_th_5_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(6, ClientsComponent_table_33_td_6_Template, 3, 2, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 41);\n    i0.ɵɵtemplate(8, ClientsComponent_table_33_th_8_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(9, ClientsComponent_table_33_td_9_Template, 4, 2, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 42);\n    i0.ɵɵtemplate(11, ClientsComponent_table_33_th_11_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(12, ClientsComponent_table_33_td_12_Template, 3, 2, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 43);\n    i0.ɵɵtemplate(14, ClientsComponent_table_33_th_14_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(15, ClientsComponent_table_33_td_15_Template, 3, 4, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(16, 44);\n    i0.ɵɵtemplate(17, ClientsComponent_table_33_th_17_Template, 2, 0, \"th\", 38);\n    i0.ɵɵtemplate(18, ClientsComponent_table_33_td_18_Template, 3, 2, \"td\", 39);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(19, ClientsComponent_table_33_tr_19_Template, 1, 0, \"tr\", 45);\n    i0.ɵɵtemplate(20, ClientsComponent_table_33_tr_20_Template, 1, 0, \"tr\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dataSource\", ctx_r5.filteredClients);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r5.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r5.displayedColumns);\n  }\n}\nexport class ClientsComponent {\n  constructor(clientService, authService, formBuilder, dialog, snackBar) {\n    this.clientService = clientService;\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.clients = [];\n    this.filteredClients = [];\n    this.loading = false;\n    this.searchTerm = '';\n    this.selectedStatut = null;\n    this.isEditing = false;\n    this.editingClientId = null;\n    this.showForm = false;\n    // Énumérations pour le template\n    this.StatutClient = StatutClient;\n    this.statutOptions = [{\n      value: StatutClient.Actif,\n      label: 'Actif'\n    }, {\n      value: StatutClient.Inactif,\n      label: 'Inactif'\n    }, {\n      value: StatutClient.Suspendu,\n      label: 'Suspendu'\n    }];\n    // Colonnes à afficher dans le tableau\n    this.displayedColumns = ['nom', 'email', 'telephone', 'statut', 'dateCreation', 'actions'];\n  }\n  ngOnInit() {\n    // Vérifier les permissions\n    if (!this.authService.canManageClients()) {\n      this.snackBar.open('Accès non autorisé', 'Fermer', {\n        duration: 3000\n      });\n      return;\n    }\n    this.initializeForm();\n    this.loadClients();\n  }\n  initializeForm() {\n    this.clientForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      telephone: [''],\n      adresse: ['', [Validators.required]],\n      statut: [StatutClient.Actif, [Validators.required]]\n    });\n  }\n  loadClients() {\n    this.loading = true;\n    this.clientService.getClients().subscribe({\n      next: clients => {\n        this.clients = clients;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors du chargement des clients');\n        this.loading = false;\n      }\n    });\n  }\n  applyFilters() {\n    this.filteredClients = this.clients.filter(client => {\n      const matchesSearch = !this.searchTerm || client.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) || client.email.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesStatut = this.selectedStatut === null || client.statut === this.selectedStatut;\n      return matchesSearch && matchesStatut;\n    });\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  onStatutFilterChange() {\n    this.applyFilters();\n  }\n  showAddForm() {\n    this.isEditing = false;\n    this.editingClientId = null;\n    this.clientForm.reset();\n    this.clientForm.patchValue({\n      statut: StatutClient.Actif\n    });\n    this.showForm = true;\n  }\n  editClient(client) {\n    this.isEditing = true;\n    this.editingClientId = client.id;\n    this.clientForm.patchValue({\n      nom: client.nom,\n      email: client.email,\n      telephone: client.telephone,\n      adresse: client.adresse,\n      statut: client.statut\n    });\n    this.showForm = true;\n  }\n  cancelForm() {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingClientId = null;\n    this.clientForm.reset();\n  }\n  onSubmit() {\n    if (this.clientForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    const clientData = this.clientForm.value;\n    if (this.isEditing && this.editingClientId) {\n      this.updateClient(this.editingClientId, clientData);\n    } else {\n      this.createClient(clientData);\n    }\n  }\n  createClient(clientData) {\n    this.loading = true;\n    this.clientService.createClient(clientData).subscribe({\n      next: client => {\n        this.clients.push(client);\n        this.applyFilters();\n        this.showSuccess('Client créé avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors de la création du client');\n        this.loading = false;\n      }\n    });\n  }\n  updateClient(id, clientData) {\n    this.loading = true;\n    this.clientService.updateClient(id, clientData).subscribe({\n      next: updatedClient => {\n        const index = this.clients.findIndex(c => c.id === id);\n        if (index !== -1) {\n          this.clients[index] = updatedClient;\n          this.applyFilters();\n        }\n        this.showSuccess('Client modifié avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: error => {\n        this.showError('Erreur lors de la modification du client');\n        this.loading = false;\n      }\n    });\n  }\n  deleteClient(client) {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.nom}\" ?`)) {\n      this.loading = true;\n      this.clientService.deleteClient(client.id).subscribe({\n        next: () => {\n          this.clients = this.clients.filter(c => c.id !== client.id);\n          this.applyFilters();\n          this.showSuccess('Client supprimé avec succès');\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la suppression du client');\n          this.loading = false;\n        }\n      });\n    }\n  }\n  getStatutLabel(statut) {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n  getStatutClass(statut) {\n    switch (statut) {\n      case StatutClient.Actif:\n        return 'statut-actif';\n      case StatutClient.Inactif:\n        return 'statut-inactif';\n      case StatutClient.Suspendu:\n        return 'statut-suspendu';\n      default:\n        return '';\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.clientForm.controls).forEach(key => {\n      const control = this.clientForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  showSuccess(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n  showError(message) {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() {\n    return this.clientForm.get('nom');\n  }\n  get email() {\n    return this.clientForm.get('email');\n  }\n  get telephone() {\n    return this.clientForm.get('telephone');\n  }\n  get adresse() {\n    return this.clientForm.get('adresse');\n  }\n  get statut() {\n    return this.clientForm.get('statut');\n  }\n  static {\n    this.ɵfac = function ClientsComponent_Factory(t) {\n      return new (t || ClientsComponent)(i0.ɵɵdirectiveInject(i1.ClientService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientsComponent,\n      selectors: [[\"app-clients\"]],\n      decls: 34,\n      vars: 10,\n      consts: [[1, \"clients-container\"], [1, \"header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [1, \"filters-card\"], [1, \"filters-row\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Nom ou email du client\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Actualiser\", 3, \"click\"], [\"class\", \"form-card\", 4, \"ngIf\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-table\", \"\", \"class\", \"clients-table\", 3, \"dataSource\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"form-card\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"nom\", \"placeholder\", \"Nom du client\"], [4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"telephone\", \"placeholder\", \"Num\\u00E9ro de t\\u00E9l\\u00E9phone\"], [\"matInput\", \"\", \"formControlName\", \"adresse\", \"placeholder\", \"Adresse compl\\u00E8te\", \"rows\", \"3\"], [\"formControlName\", \"statut\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"no-data\"], [\"mat-table\", \"\", 1, \"clients-table\", 3, \"dataSource\"], [\"matColumnDef\", \"nom\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"telephone\"], [\"matColumnDef\", \"statut\"], [\"matColumnDef\", \"dateCreation\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"client-info\"], [1, \"client-icon\"], [1, \"client-name\"], [1, \"email-link\", 3, \"href\"], [4, \"ngIf\", \"ngIfElse\"], [\"noPhone\", \"\"], [1, \"no-data-text\"], [1, \"statut-badge\", 3, \"ngClass\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Modifier\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Supprimer\", \"color\", \"warn\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Modifier\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Supprimer\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function ClientsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" Gestion des Clients \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, ClientsComponent_button_6_Template, 4, 0, \"button\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card\", 3)(8, \"div\", 4)(9, \"mat-form-field\", 5)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Rechercher\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function ClientsComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function ClientsComponent_Template_input_input_12_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-icon\", 7);\n          i0.ɵɵtext(14, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-form-field\", 8)(16, \"mat-label\");\n          i0.ɵɵtext(17, \"Filtrer par statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-select\", 9);\n          i0.ɵɵlistener(\"valueChange\", function ClientsComponent_Template_mat_select_valueChange_18_listener($event) {\n            return ctx.selectedStatut = $event;\n          })(\"selectionChange\", function ClientsComponent_Template_mat_select_selectionChange_18_listener() {\n            return ctx.onStatutFilterChange();\n          });\n          i0.ɵɵelementStart(19, \"mat-option\", 10);\n          i0.ɵɵtext(20, \"Tous les statuts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, ClientsComponent_mat_option_21_Template, 2, 2, \"mat-option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ClientsComponent_Template_button_click_22_listener() {\n            return ctx.loadClients();\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(25, ClientsComponent_mat_card_25_Template, 54, 14, \"mat-card\", 13);\n          i0.ɵɵelementStart(26, \"mat-card\", 14)(27, \"mat-card-header\")(28, \"mat-card-title\");\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-card-content\");\n          i0.ɵɵtemplate(31, ClientsComponent_div_31_Template, 4, 0, \"div\", 15);\n          i0.ɵɵtemplate(32, ClientsComponent_div_32_Template, 6, 1, \"div\", 16);\n          i0.ɵɵtemplate(33, ClientsComponent_table_33_Template, 21, 3, \"table\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.canManageClients());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.selectedStatut);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statutOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" Liste des clients (\", ctx.filteredClients.length, \") \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredClients.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredClients.length > 0);\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "StatutClient", "i0", "ɵɵelementStart", "ɵɵlistener", "ClientsComponent_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "showAddForm", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r8", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "option_r19", "ɵɵtextInterpolate", "ctx_r17", "isEditing", "ctx_r18", "ClientsComponent_mat_card_25_Template_form_ngSubmit_7_listener", "_r21", "ctx_r20", "onSubmit", "ɵɵelement", "ɵɵtemplate", "ClientsComponent_mat_card_25_mat_error_15_Template", "ClientsComponent_mat_card_25_mat_error_16_Template", "ClientsComponent_mat_card_25_mat_error_24_Template", "ClientsComponent_mat_card_25_mat_error_25_Template", "ClientsComponent_mat_card_25_mat_error_39_Template", "ClientsComponent_mat_card_25_mat_option_45_Template", "ClientsComponent_mat_card_25_mat_error_46_Template", "ClientsComponent_mat_card_25_Template_button_click_48_listener", "ctx_r22", "cancelForm", "ClientsComponent_mat_card_25_mat_icon_51_Template", "ClientsComponent_mat_card_25_span_52_Template", "ClientsComponent_mat_card_25_span_53_Template", "ctx_r2", "clientForm", "nom", "<PERSON><PERSON><PERSON><PERSON>", "email", "adresse", "statutOptions", "statut", "loading", "ClientsComponent_div_32_button_5_Template_button_click_0_listener", "_r25", "ctx_r24", "ClientsComponent_div_32_button_5_Template", "ctx_r4", "authService", "canManageClients", "client_r40", "ɵɵpropertyInterpolate1", "client_r41", "ɵɵsanitizeUrl", "client_r42", "telephone", "ClientsComponent_table_33_td_9_span_1_Template", "ClientsComponent_table_33_td_9_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r44", "ctx_r33", "getStatutClass", "client_r47", "getStatutLabel", "ɵɵpipeBind2", "client_r48", "dateCreation", "ClientsComponent_table_33_td_18_button_1_Template_button_click_0_listener", "_r54", "client_r49", "$implicit", "ctx_r52", "editClient", "ClientsComponent_table_33_td_18_button_2_Template_button_click_0_listener", "_r57", "ctx_r55", "deleteClient", "ClientsComponent_table_33_td_18_button_1_Template", "ClientsComponent_table_33_td_18_button_2_Template", "ctx_r37", "ɵɵelementContainerStart", "ClientsComponent_table_33_th_2_Template", "ClientsComponent_table_33_td_3_Template", "ɵɵelementContainerEnd", "ClientsComponent_table_33_th_5_Template", "ClientsComponent_table_33_td_6_Template", "ClientsComponent_table_33_th_8_Template", "ClientsComponent_table_33_td_9_Template", "ClientsComponent_table_33_th_11_Template", "ClientsComponent_table_33_td_12_Template", "ClientsComponent_table_33_th_14_Template", "ClientsComponent_table_33_td_15_Template", "ClientsComponent_table_33_th_17_Template", "ClientsComponent_table_33_td_18_Template", "ClientsComponent_table_33_tr_19_Template", "ClientsComponent_table_33_tr_20_Template", "ctx_r5", "filteredClients", "displayedColumns", "ClientsComponent", "constructor", "clientService", "formBuilder", "dialog", "snackBar", "clients", "searchTerm", "selectedStatut", "editingClientId", "showForm", "Actif", "Inactif", "Suspendu", "ngOnInit", "open", "duration", "initializeForm", "loadClients", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "getClients", "subscribe", "next", "applyFilters", "error", "showError", "filter", "client", "matchesSearch", "toLowerCase", "includes", "matchesStatut", "onSearchChange", "onStatutFilterChange", "reset", "patchValue", "id", "invalid", "markFormGroupTouched", "clientData", "updateClient", "createClient", "push", "showSuccess", "updatedClient", "index", "findIndex", "c", "confirm", "option", "find", "opt", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "panelClass", "ɵɵdirectiveInject", "i1", "ClientService", "i2", "AuthService", "i3", "FormBuilder", "i4", "MatDialog", "i5", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "ClientsComponent_Template", "rf", "ctx", "ClientsComponent_button_6_Template", "ClientsComponent_Template_input_ngModelChange_12_listener", "$event", "ClientsComponent_Template_input_input_12_listener", "ClientsComponent_Template_mat_select_valueChange_18_listener", "ClientsComponent_Template_mat_select_selectionChange_18_listener", "ClientsComponent_mat_option_21_Template", "ClientsComponent_Template_button_click_22_listener", "ClientsComponent_mat_card_25_Template", "ClientsComponent_div_31_Template", "ClientsComponent_div_32_Template", "ClientsComponent_table_33_Template", "length"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\clients\\clients.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\clients\\clients.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { ClientService } from '../../services/client.service';\nimport { AuthService } from '../../services/auth.service';\nimport { Client, StatutClient } from '../models';\n\n@Component({\n  selector: 'app-clients',\n  templateUrl: './clients.component.html',\n  styleUrls: ['./clients.component.css']\n})\nexport class ClientsComponent implements OnInit {\n  clients: Client[] = [];\n  filteredClients: Client[] = [];\n  loading = false;\n  searchTerm = '';\n  selectedStatut: StatutClient | null = null;\n\n  // Formulaire pour ajouter/modifier un client\n  clientForm!: FormGroup;\n  isEditing = false;\n  editingClientId: string | null = null;\n  showForm = false;\n\n  // Énumérations pour le template\n  StatutClient = StatutClient;\n  statutOptions = [\n    { value: StatutClient.Actif, label: 'Actif' },\n    { value: StatutClient.Inactif, label: 'Inactif' },\n    { value: StatutClient.Suspendu, label: 'Suspendu' }\n  ];\n\n  // Colonnes à afficher dans le tableau\n  displayedColumns: string[] = ['nom', 'email', 'telephone', 'statut', 'dateCreation', 'actions'];\n\n  constructor(\n    private clientService: ClientService,\n    private authService: AuthService,\n    private formBuilder: FormBuilder,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    // Vérifier les permissions\n    if (!this.authService.canManageClients()) {\n      this.snackBar.open('Accès non autorisé', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.initializeForm();\n    this.loadClients();\n  }\n\n  initializeForm(): void {\n    this.clientForm = this.formBuilder.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      telephone: [''],\n      adresse: ['', [Validators.required]],\n      statut: [StatutClient.Actif, [Validators.required]]\n    });\n  }\n\n  loadClients(): void {\n    this.loading = true;\n    this.clientService.getClients().subscribe({\n      next: (clients) => {\n        this.clients = clients;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors du chargement des clients');\n        this.loading = false;\n      }\n    });\n  }\n\n  applyFilters(): void {\n    this.filteredClients = this.clients.filter(client => {\n      const matchesSearch = !this.searchTerm ||\n        client.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n        client.email.toLowerCase().includes(this.searchTerm.toLowerCase());\n\n      const matchesStatut = this.selectedStatut === null || client.statut === this.selectedStatut;\n\n      return matchesSearch && matchesStatut;\n    });\n  }\n\n  onSearchChange(): void {\n    this.applyFilters();\n  }\n\n  onStatutFilterChange(): void {\n    this.applyFilters();\n  }\n\n  showAddForm(): void {\n    this.isEditing = false;\n    this.editingClientId = null;\n    this.clientForm.reset();\n    this.clientForm.patchValue({ statut: StatutClient.Actif });\n    this.showForm = true;\n  }\n\n  editClient(client: Client): void {\n    this.isEditing = true;\n    this.editingClientId = client.id;\n    this.clientForm.patchValue({\n      nom: client.nom,\n      email: client.email,\n      telephone: client.telephone,\n      adresse: client.adresse,\n      statut: client.statut\n    });\n    this.showForm = true;\n  }\n\n  cancelForm(): void {\n    this.showForm = false;\n    this.isEditing = false;\n    this.editingClientId = null;\n    this.clientForm.reset();\n  }\n\n  onSubmit(): void {\n    if (this.clientForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    const clientData = this.clientForm.value;\n\n    if (this.isEditing && this.editingClientId) {\n      this.updateClient(this.editingClientId, clientData);\n    } else {\n      this.createClient(clientData);\n    }\n  }\n\n  createClient(clientData: any): void {\n    this.loading = true;\n    this.clientService.createClient(clientData).subscribe({\n      next: (client) => {\n        this.clients.push(client);\n        this.applyFilters();\n        this.showSuccess('Client créé avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors de la création du client');\n        this.loading = false;\n      }\n    });\n  }\n\n  updateClient(id: string, clientData: any): void {\n    this.loading = true;\n    this.clientService.updateClient(id, clientData).subscribe({\n      next: (updatedClient) => {\n        const index = this.clients.findIndex(c => c.id === id);\n        if (index !== -1) {\n          this.clients[index] = updatedClient;\n          this.applyFilters();\n        }\n        this.showSuccess('Client modifié avec succès');\n        this.cancelForm();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.showError('Erreur lors de la modification du client');\n        this.loading = false;\n      }\n    });\n  }\n\n  deleteClient(client: Client): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le client \"${client.nom}\" ?`)) {\n      this.loading = true;\n      this.clientService.deleteClient(client.id).subscribe({\n        next: () => {\n          this.clients = this.clients.filter(c => c.id !== client.id);\n          this.applyFilters();\n          this.showSuccess('Client supprimé avec succès');\n          this.loading = false;\n        },\n        error: (error) => {\n          this.showError('Erreur lors de la suppression du client');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  getStatutLabel(statut: StatutClient): string {\n    const option = this.statutOptions.find(opt => opt.value === statut);\n    return option ? option.label : 'Inconnu';\n  }\n\n  getStatutClass(statut: StatutClient): string {\n    switch (statut) {\n      case StatutClient.Actif:\n        return 'statut-actif';\n      case StatutClient.Inactif:\n        return 'statut-inactif';\n      case StatutClient.Suspendu:\n        return 'statut-suspendu';\n      default:\n        return '';\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.clientForm.controls).forEach(key => {\n      const control = this.clientForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 3000,\n      panelClass: ['success-snackbar']\n    });\n  }\n\n  private showError(message: string): void {\n    this.snackBar.open(message, 'Fermer', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get nom() { return this.clientForm.get('nom'); }\n  get email() { return this.clientForm.get('email'); }\n  get telephone() { return this.clientForm.get('telephone'); }\n  get adresse() { return this.clientForm.get('adresse'); }\n  get statut() { return this.clientForm.get('statut'); }\n}\n", "<div class=\"clients-container\">\n  <!-- En-tête -->\n  <div class=\"header\">\n    <h1>\n      <mat-icon>people</mat-icon>\n      Gestion des Clients\n    </h1>\n    <button mat-raised-button color=\"primary\" (click)=\"showAddForm()\"\n            *ngIf=\"authService.canManageClients()\">\n      <mat-icon>add</mat-icon>\n      Nouveau Client\n    </button>\n  </div>\n\n  <!-- Filtres et recherche -->\n  <mat-card class=\"filters-card\">\n    <div class=\"filters-row\">\n      <mat-form-field appearance=\"outline\" class=\"search-field\">\n        <mat-label>Rechercher</mat-label>\n        <input matInput [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange()\"\n               placeholder=\"Nom ou email du client\">\n        <mat-icon matSuffix>search</mat-icon>\n      </mat-form-field>\n\n      <mat-form-field appearance=\"outline\" class=\"filter-field\">\n        <mat-label>Filtrer par statut</mat-label>\n        <mat-select [(value)]=\"selectedStatut\" (selectionChange)=\"onStatutFilterChange()\">\n          <mat-option [value]=\"null\">Tous les statuts</mat-option>\n          <mat-option *ngFor=\"let option of statutOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </mat-option>\n        </mat-select>\n      </mat-form-field>\n\n      <button mat-icon-button (click)=\"loadClients()\" matTooltip=\"Actualiser\">\n        <mat-icon>refresh</mat-icon>\n      </button>\n    </div>\n  </mat-card>\n\n  <!-- Formulaire d'ajout/modification -->\n  <mat-card *ngIf=\"showForm\" class=\"form-card\">\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>{{ isEditing ? 'edit' : 'add' }}</mat-icon>\n        {{ isEditing ? 'Modifier le client' : 'Nouveau client' }}\n      </mat-card-title>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"clientForm\" (ngSubmit)=\"onSubmit()\">\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Nom complet</mat-label>\n            <input matInput formControlName=\"nom\" placeholder=\"Nom du client\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error *ngIf=\"nom?.hasError('required')\">Le nom est requis</mat-error>\n            <mat-error *ngIf=\"nom?.hasError('minlength')\">Le nom doit contenir au moins 2 caractères</mat-error>\n          </mat-form-field>\n        </div>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Email</mat-label>\n            <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n            <mat-icon matSuffix>email</mat-icon>\n            <mat-error *ngIf=\"email?.hasError('required')\">L'email est requis</mat-error>\n            <mat-error *ngIf=\"email?.hasError('email')\">Format d'email invalide</mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Téléphone</mat-label>\n            <input matInput formControlName=\"telephone\" placeholder=\"Numéro de téléphone\">\n            <mat-icon matSuffix>phone</mat-icon>\n          </mat-form-field>\n        </div>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Adresse</mat-label>\n            <textarea matInput formControlName=\"adresse\" placeholder=\"Adresse complète\" rows=\"3\"></textarea>\n            <mat-icon matSuffix>location_on</mat-icon>\n            <mat-error *ngIf=\"adresse?.hasError('required')\">L'adresse est requise</mat-error>\n          </mat-form-field>\n        </div>\n\n        <div class=\"form-row\">\n          <mat-form-field appearance=\"outline\" class=\"half-width\">\n            <mat-label>Statut</mat-label>\n            <mat-select formControlName=\"statut\">\n              <mat-option *ngFor=\"let option of statutOptions\" [value]=\"option.value\">\n                {{ option.label }}\n              </mat-option>\n            </mat-select>\n            <mat-error *ngIf=\"statut?.hasError('required')\">Le statut est requis</mat-error>\n          </mat-form-field>\n        </div>\n\n        <div class=\"form-actions\">\n          <button mat-button type=\"button\" (click)=\"cancelForm()\">\n            Annuler\n          </button>\n          <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading\">\n            <mat-icon *ngIf=\"loading\">hourglass_empty</mat-icon>\n            <span *ngIf=\"!loading\">{{ isEditing ? 'Modifier' : 'Créer' }}</span>\n            <span *ngIf=\"loading\">{{ isEditing ? 'Modification...' : 'Création...' }}</span>\n          </button>\n        </div>\n      </form>\n    </mat-card-content>\n  </mat-card>\n\n  <!-- Liste des clients -->\n  <mat-card class=\"table-card\">\n    <mat-card-header>\n      <mat-card-title>\n        Liste des clients ({{ filteredClients.length }})\n      </mat-card-title>\n    </mat-card-header>\n\n    <mat-card-content>\n      <div *ngIf=\"loading\" class=\"loading-container\">\n        <mat-spinner diameter=\"50\"></mat-spinner>\n        <p>Chargement des clients...</p>\n      </div>\n\n      <div *ngIf=\"!loading && filteredClients.length === 0\" class=\"no-data\">\n        <mat-icon>people_outline</mat-icon>\n        <p>Aucun client trouvé</p>\n        <button mat-raised-button color=\"primary\" (click)=\"showAddForm()\"\n                *ngIf=\"authService.canManageClients()\">\n          <mat-icon>add</mat-icon>\n          Ajouter le premier client\n        </button>\n      </div>\n\n      <table mat-table [dataSource]=\"filteredClients\" *ngIf=\"!loading && filteredClients.length > 0\"\n             class=\"clients-table\">\n\n        <!-- Colonne Nom -->\n        <ng-container matColumnDef=\"nom\">\n          <th mat-header-cell *matHeaderCellDef>Nom</th>\n          <td mat-cell *matCellDef=\"let client\">\n            <div class=\"client-info\">\n              <mat-icon class=\"client-icon\">person</mat-icon>\n              <span class=\"client-name\">{{ client.nom }}</span>\n            </div>\n          </td>\n        </ng-container>\n\n        <!-- Colonne Email -->\n        <ng-container matColumnDef=\"email\">\n          <th mat-header-cell *matHeaderCellDef>Email</th>\n          <td mat-cell *matCellDef=\"let client\">\n            <a href=\"mailto:{{ client.email }}\" class=\"email-link\">{{ client.email }}</a>\n          </td>\n        </ng-container>\n\n        <!-- Colonne Téléphone -->\n        <ng-container matColumnDef=\"telephone\">\n          <th mat-header-cell *matHeaderCellDef>Téléphone</th>\n          <td mat-cell *matCellDef=\"let client\">\n            <span *ngIf=\"client.telephone; else noPhone\">{{ client.telephone }}</span>\n            <ng-template #noPhone>\n              <span class=\"no-data-text\">Non renseigné</span>\n            </ng-template>\n          </td>\n        </ng-container>\n\n        <!-- Colonne Statut -->\n        <ng-container matColumnDef=\"statut\">\n          <th mat-header-cell *matHeaderCellDef>Statut</th>\n          <td mat-cell *matCellDef=\"let client\">\n            <span class=\"statut-badge\" [ngClass]=\"getStatutClass(client.statut)\">\n              {{ getStatutLabel(client.statut) }}\n            </span>\n          </td>\n        </ng-container>\n\n        <!-- Colonne Date de création -->\n        <ng-container matColumnDef=\"dateCreation\">\n          <th mat-header-cell *matHeaderCellDef>Date de création</th>\n          <td mat-cell *matCellDef=\"let client\">\n            {{ client.dateCreation | date:'dd/MM/yyyy' }}\n          </td>\n        </ng-container>\n\n        <!-- Colonne Actions -->\n        <ng-container matColumnDef=\"actions\">\n          <th mat-header-cell *matHeaderCellDef>Actions</th>\n          <td mat-cell *matCellDef=\"let client\">\n            <button mat-icon-button (click)=\"editClient(client)\"\n                    matTooltip=\"Modifier\" *ngIf=\"authService.canManageClients()\">\n              <mat-icon>edit</mat-icon>\n            </button>\n            <button mat-icon-button (click)=\"deleteClient(client)\"\n                    matTooltip=\"Supprimer\" color=\"warn\" *ngIf=\"authService.canManageClients()\">\n              <mat-icon>delete</mat-icon>\n            </button>\n          </td>\n        </ng-container>\n\n        <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n        <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n      </table>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAAiBC,YAAY,QAAQ,WAAW;;;;;;;;;;ICC5CC,EAAA,CAAAC,cAAA,iBAC+C;IADLD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE/DT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACxBX,EAAA,CAAAU,MAAA,uBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAiBHX,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAFoCX,EAAA,CAAAY,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACrEd,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IA0BEjB,EAAA,CAAAC,cAAA,gBAA6C;IAAAD,EAAA,CAAAU,MAAA,wBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAC1EX,EAAA,CAAAC,cAAA,gBAA8C;IAAAD,EAAA,CAAAU,MAAA,sDAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IASpGX,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAU,MAAA,yBAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAC7EX,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAe/EX,EAAA,CAAAC,cAAA,gBAAiD;IAAAD,EAAA,CAAAU,MAAA,4BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAQhFX,EAAA,CAAAC,cAAA,qBAAwE;IACtED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAa;;;;IAFoCX,EAAA,CAAAY,UAAA,UAAAM,UAAA,CAAAJ,KAAA,CAAsB;IACrEd,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAE,UAAA,CAAAD,KAAA,MACF;;;;;IAEFjB,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;IAShFX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IACpDX,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAU,MAAA,GAAsC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA7CX,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAAmB,iBAAA,CAAAC,OAAA,CAAAC,SAAA,6BAAsC;;;;;IAC7DrB,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAU,MAAA,GAAmD;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA1DX,EAAA,CAAAe,SAAA,GAAmD;IAAnDf,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAD,SAAA,0CAAmD;;;;;;IAhEnFrB,EAAA,CAAAC,cAAA,mBAA6C;IAG7BD,EAAA,CAAAU,MAAA,GAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACrDX,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,uBAAkB;IACeD,EAAA,CAAAE,UAAA,sBAAAqB,+DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAiB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpD1B,EAAA,CAAAC,cAAA,cAAsB;IAEPD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAClCX,EAAA,CAAA2B,SAAA,iBAAkE;IAClE3B,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACrCX,EAAA,CAAA4B,UAAA,KAAAC,kDAAA,wBAA0E;IAC1E7B,EAAA,CAAA4B,UAAA,KAAAE,kDAAA,wBAAoG;IACtG9B,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC5BX,EAAA,CAAA2B,SAAA,iBAAqF;IACrF3B,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACpCX,EAAA,CAAA4B,UAAA,KAAAG,kDAAA,wBAA6E;IAC7E/B,EAAA,CAAA4B,UAAA,KAAAI,kDAAA,wBAA+E;IACjFhC,EAAA,CAAAW,YAAA,EAAiB;IAEjBX,EAAA,CAAAC,cAAA,0BAAwD;IAC3CD,EAAA,CAAAU,MAAA,2BAAS;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAChCX,EAAA,CAAA2B,SAAA,iBAA8E;IAC9E3B,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAIxCX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,eAAO;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC9BX,EAAA,CAAA2B,SAAA,oBAAgG;IAChG3B,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC1CX,EAAA,CAAA4B,UAAA,KAAAK,kDAAA,wBAAkF;IACpFjC,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,eAAsB;IAEPD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC7BX,EAAA,CAAAC,cAAA,sBAAqC;IACnCD,EAAA,CAAA4B,UAAA,KAAAM,mDAAA,yBAEa;IACflC,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAA4B,UAAA,KAAAO,kDAAA,wBAAgF;IAClFnC,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,eAA0B;IACSD,EAAA,CAAAE,UAAA,mBAAAkC,+DAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAoB,IAAA;MAAA,MAAAa,OAAA,GAAArC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6B,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACrDtC,EAAA,CAAAU,MAAA,iBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA6E;IAC3ED,EAAA,CAAA4B,UAAA,KAAAW,iDAAA,uBAAoD;IACpDvC,EAAA,CAAA4B,UAAA,KAAAY,6CAAA,mBAAoE;IACpExC,EAAA,CAAA4B,UAAA,KAAAa,6CAAA,mBAAgF;IAClFzC,EAAA,CAAAW,YAAA,EAAS;;;;IA9DDX,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAmB,iBAAA,CAAAuB,MAAA,CAAArB,SAAA,kBAAgC;IAC1CrB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAA0B,MAAA,CAAArB,SAAA,gDACF;IAIMrB,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAY,UAAA,cAAA8B,MAAA,CAAAC,UAAA,CAAwB;IAMZ3C,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAE,GAAA,kBAAAF,MAAA,CAAAE,GAAA,CAAAC,QAAA,aAA+B;IAC/B7C,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAE,GAAA,kBAAAF,MAAA,CAAAE,GAAA,CAAAC,QAAA,cAAgC;IAShC7C,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAI,KAAA,kBAAAJ,MAAA,CAAAI,KAAA,CAAAD,QAAA,aAAiC;IACjC7C,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAI,KAAA,kBAAAJ,MAAA,CAAAI,KAAA,CAAAD,QAAA,UAA8B;IAe9B7C,EAAA,CAAAe,SAAA,IAAmC;IAAnCf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAK,OAAA,kBAAAL,MAAA,CAAAK,OAAA,CAAAF,QAAA,aAAmC;IAQd7C,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAY,UAAA,YAAA8B,MAAA,CAAAM,aAAA,CAAgB;IAIrChD,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAO,MAAA,kBAAAP,MAAA,CAAAO,MAAA,CAAAJ,QAAA,aAAkC;IAQQ7C,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAY,UAAA,aAAA8B,MAAA,CAAAQ,OAAA,CAAoB;IAC/DlD,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAQ,OAAA,CAAa;IACjBlD,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAY,UAAA,UAAA8B,MAAA,CAAAQ,OAAA,CAAc;IACdlD,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAY,UAAA,SAAA8B,MAAA,CAAAQ,OAAA,CAAa;;;;;IAgB1BlD,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA2B,SAAA,sBAAyC;IACzC3B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,gCAAyB;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;;IAMhCX,EAAA,CAAAC,cAAA,iBAC+C;IADLD,EAAA,CAAAE,UAAA,mBAAAiD,kEAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6C,OAAA,CAAA5C,WAAA,EAAa;IAAA,EAAC;IAE/DT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACxBX,EAAA,CAAAU,MAAA,kCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAPXX,EAAA,CAAAC,cAAA,cAAsE;IAC1DD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACnCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,+BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC1BX,EAAA,CAAA4B,UAAA,IAAA0B,yCAAA,oBAIS;IACXtD,EAAA,CAAAW,YAAA,EAAM;;;;IAJKX,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAY,UAAA,SAAA2C,MAAA,CAAAC,WAAA,CAAAC,gBAAA,GAAoC;;;;;IAW3CzD,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAC9CX,EAAA,CAAAC,cAAA,aAAsC;IAEJD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC/CX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAvBX,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAmB,iBAAA,CAAAuC,UAAA,CAAAd,GAAA,CAAgB;;;;;IAO9C5C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAChDX,EAAA,CAAAC,cAAA,aAAsC;IACmBD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;IAA1EX,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAA2D,sBAAA,oBAAAC,UAAA,CAAAd,KAAA,MAAA9C,EAAA,CAAA6D,aAAA,CAAgC;IAAoB7D,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAmB,iBAAA,CAAAyC,UAAA,CAAAd,KAAA,CAAkB;;;;;IAM3E9C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,0BAAS;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAElDX,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA7BX,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAmB,iBAAA,CAAA2C,UAAA,CAAAC,SAAA,CAAsB;;;;;IAEjE/D,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAU,MAAA,yBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAHnDX,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAA4B,UAAA,IAAAoC,8CAAA,mBAA0E;IAC1EhE,EAAA,CAAA4B,UAAA,IAAAqC,qDAAA,iCAAAjE,EAAA,CAAAkE,sBAAA,CAEc;IAChBlE,EAAA,CAAAW,YAAA,EAAK;;;;;IAJIX,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAY,UAAA,SAAAkD,UAAA,CAAAC,SAAA,CAAwB,aAAAI,IAAA;;;;;IASjCnE,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IACjDX,EAAA,CAAAC,cAAA,aAAsC;IAElCD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAFoBX,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAY,UAAA,YAAAwD,OAAA,CAAAC,cAAA,CAAAC,UAAA,CAAArB,MAAA,EAAyC;IAClEjD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAoD,OAAA,CAAAG,cAAA,CAAAD,UAAA,CAAArB,MAAA,OACF;;;;;IAMFjD,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,4BAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAC3DX,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAwE,WAAA,OAAAC,UAAA,CAAAC,YAAA,qBACF;;;;;IAKA1E,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;;IAEhDX,EAAA,CAAAC,cAAA,iBACqE;IAD7CD,EAAA,CAAAE,UAAA,mBAAAyE,0EAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAwE,IAAA;MAAA,MAAAC,UAAA,GAAA7E,EAAA,CAAAO,aAAA,GAAAuE,SAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuE,OAAA,CAAAC,UAAA,CAAAH,UAAA,CAAkB;IAAA,EAAC;IAElD7E,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;;IAE3BX,EAAA,CAAAC,cAAA,iBACmF;IAD3DD,EAAA,CAAAE,UAAA,mBAAA+E,0EAAA;MAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA;MAAA,MAAAL,UAAA,GAAA7E,EAAA,CAAAO,aAAA,GAAAuE,SAAA;MAAA,MAAAK,OAAA,GAAAnF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2E,OAAA,CAAAC,YAAA,CAAAP,UAAA,CAAoB;IAAA,EAAC;IAEpD7E,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAP/BX,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAA4B,UAAA,IAAAyD,iDAAA,qBAGS;IACTrF,EAAA,CAAA4B,UAAA,IAAA0D,iDAAA,qBAGS;IACXtF,EAAA,CAAAW,YAAA,EAAK;;;;IAP4BX,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAY,UAAA,SAAA2E,OAAA,CAAA/B,WAAA,CAAAC,gBAAA,GAAoC;IAItBzD,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAY,UAAA,SAAA2E,OAAA,CAAA/B,WAAA,CAAAC,gBAAA,GAAoC;;;;;IAMrFzD,EAAA,CAAA2B,SAAA,aAA4D;;;;;IAC5D3B,EAAA,CAAA2B,SAAA,aAAkE;;;;;IAnEpE3B,EAAA,CAAAC,cAAA,gBAC6B;IAG3BD,EAAA,CAAAwF,uBAAA,OAAiC;IAC/BxF,EAAA,CAAA4B,UAAA,IAAA6D,uCAAA,iBAA8C;IAC9CzF,EAAA,CAAA4B,UAAA,IAAA8D,uCAAA,iBAKK;IACP1F,EAAA,CAAA2F,qBAAA,EAAe;IAGf3F,EAAA,CAAAwF,uBAAA,OAAmC;IACjCxF,EAAA,CAAA4B,UAAA,IAAAgE,uCAAA,iBAAgD;IAChD5F,EAAA,CAAA4B,UAAA,IAAAiE,uCAAA,iBAEK;IACP7F,EAAA,CAAA2F,qBAAA,EAAe;IAGf3F,EAAA,CAAAwF,uBAAA,OAAuC;IACrCxF,EAAA,CAAA4B,UAAA,IAAAkE,uCAAA,iBAAoD;IACpD9F,EAAA,CAAA4B,UAAA,IAAAmE,uCAAA,iBAKK;IACP/F,EAAA,CAAA2F,qBAAA,EAAe;IAGf3F,EAAA,CAAAwF,uBAAA,QAAoC;IAClCxF,EAAA,CAAA4B,UAAA,KAAAoE,wCAAA,iBAAiD;IACjDhG,EAAA,CAAA4B,UAAA,KAAAqE,wCAAA,iBAIK;IACPjG,EAAA,CAAA2F,qBAAA,EAAe;IAGf3F,EAAA,CAAAwF,uBAAA,QAA0C;IACxCxF,EAAA,CAAA4B,UAAA,KAAAsE,wCAAA,iBAA2D;IAC3DlG,EAAA,CAAA4B,UAAA,KAAAuE,wCAAA,iBAEK;IACPnG,EAAA,CAAA2F,qBAAA,EAAe;IAGf3F,EAAA,CAAAwF,uBAAA,QAAqC;IACnCxF,EAAA,CAAA4B,UAAA,KAAAwE,wCAAA,iBAAkD;IAClDpG,EAAA,CAAA4B,UAAA,KAAAyE,wCAAA,iBASK;IACPrG,EAAA,CAAA2F,qBAAA,EAAe;IAEf3F,EAAA,CAAA4B,UAAA,KAAA0E,wCAAA,iBAA4D;IAC5DtG,EAAA,CAAA4B,UAAA,KAAA2E,wCAAA,iBAAkE;IACpEvG,EAAA,CAAAW,YAAA,EAAQ;;;;IApESX,EAAA,CAAAY,UAAA,eAAA4F,MAAA,CAAAC,eAAA,CAA8B;IAkEzBzG,EAAA,CAAAe,SAAA,IAAiC;IAAjCf,EAAA,CAAAY,UAAA,oBAAA4F,MAAA,CAAAE,gBAAA,CAAiC;IACpB1G,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAY,UAAA,qBAAA4F,MAAA,CAAAE,gBAAA,CAA0B;;;AD9LnE,OAAM,MAAOC,gBAAgB;EAwB3BC,YACUC,aAA4B,EAC5BrD,WAAwB,EACxBsD,WAAwB,EACxBC,MAAiB,EACjBC,QAAqB;IAJrB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAArD,WAAW,GAAXA,WAAW;IACX,KAAAsD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IA5BlB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAR,eAAe,GAAa,EAAE;IAC9B,KAAAvD,OAAO,GAAG,KAAK;IACf,KAAAgE,UAAU,GAAG,EAAE;IACf,KAAAC,cAAc,GAAwB,IAAI;IAI1C,KAAA9F,SAAS,GAAG,KAAK;IACjB,KAAA+F,eAAe,GAAkB,IAAI;IACrC,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAtH,YAAY,GAAGA,YAAY;IAC3B,KAAAiD,aAAa,GAAG,CACd;MAAElC,KAAK,EAAEf,YAAY,CAACuH,KAAK;MAAErG,KAAK,EAAE;IAAO,CAAE,EAC7C;MAAEH,KAAK,EAAEf,YAAY,CAACwH,OAAO;MAAEtG,KAAK,EAAE;IAAS,CAAE,EACjD;MAAEH,KAAK,EAAEf,YAAY,CAACyH,QAAQ;MAAEvG,KAAK,EAAE;IAAU,CAAE,CACpD;IAED;IACA,KAAAyF,gBAAgB,GAAa,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,CAAC;EAQ5F;EAEHe,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACjE,WAAW,CAACC,gBAAgB,EAAE,EAAE;MACxC,IAAI,CAACuD,QAAQ,CAACU,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACtE;;IAGF,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACjF,UAAU,GAAG,IAAI,CAACmE,WAAW,CAACgB,KAAK,CAAC;MACvClF,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAACiI,QAAQ,EAAEjI,UAAU,CAACkI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDlF,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChD,UAAU,CAACiI,QAAQ,EAAEjI,UAAU,CAACgD,KAAK,CAAC,CAAC;MACpDiB,SAAS,EAAE,CAAC,EAAE,CAAC;MACfhB,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjD,UAAU,CAACiI,QAAQ,CAAC,CAAC;MACpC9E,MAAM,EAAE,CAAClD,YAAY,CAACuH,KAAK,EAAE,CAACxH,UAAU,CAACiI,QAAQ,CAAC;KACnD,CAAC;EACJ;EAEAF,WAAWA,CAAA;IACT,IAAI,CAAC3E,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC2D,aAAa,CAACoB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGlB,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACmB,YAAY,EAAE;QACnB,IAAI,CAAClF,OAAO,GAAG,KAAK;MACtB,CAAC;MACDmF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,uCAAuC,CAAC;QACvD,IAAI,CAACpF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAkF,YAAYA,CAAA;IACV,IAAI,CAAC3B,eAAe,GAAG,IAAI,CAACQ,OAAO,CAACsB,MAAM,CAACC,MAAM,IAAG;MAClD,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACvB,UAAU,IACpCsB,MAAM,CAAC5F,GAAG,CAAC8F,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC,IAChEF,MAAM,CAAC1F,KAAK,CAAC4F,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC;MAEpE,MAAME,aAAa,GAAG,IAAI,CAACzB,cAAc,KAAK,IAAI,IAAIqB,MAAM,CAACvF,MAAM,KAAK,IAAI,CAACkE,cAAc;MAE3F,OAAOsB,aAAa,IAAIG,aAAa;IACvC,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACT,YAAY,EAAE;EACrB;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,CAACV,YAAY,EAAE;EACrB;EAEA3H,WAAWA,CAAA;IACT,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+F,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACzE,UAAU,CAACoG,KAAK,EAAE;IACvB,IAAI,CAACpG,UAAU,CAACqG,UAAU,CAAC;MAAE/F,MAAM,EAAElD,YAAY,CAACuH;IAAK,CAAE,CAAC;IAC1D,IAAI,CAACD,QAAQ,GAAG,IAAI;EACtB;EAEArC,UAAUA,CAACwD,MAAc;IACvB,IAAI,CAACnH,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC+F,eAAe,GAAGoB,MAAM,CAACS,EAAE;IAChC,IAAI,CAACtG,UAAU,CAACqG,UAAU,CAAC;MACzBpG,GAAG,EAAE4F,MAAM,CAAC5F,GAAG;MACfE,KAAK,EAAE0F,MAAM,CAAC1F,KAAK;MACnBiB,SAAS,EAAEyE,MAAM,CAACzE,SAAS;MAC3BhB,OAAO,EAAEyF,MAAM,CAACzF,OAAO;MACvBE,MAAM,EAAEuF,MAAM,CAACvF;KAChB,CAAC;IACF,IAAI,CAACoE,QAAQ,GAAG,IAAI;EACtB;EAEA/E,UAAUA,CAAA;IACR,IAAI,CAAC+E,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChG,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+F,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACzE,UAAU,CAACoG,KAAK,EAAE;EACzB;EAEArH,QAAQA,CAAA;IACN,IAAI,IAAI,CAACiB,UAAU,CAACuG,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,MAAMC,UAAU,GAAG,IAAI,CAACzG,UAAU,CAAC7B,KAAK;IAExC,IAAI,IAAI,CAACO,SAAS,IAAI,IAAI,CAAC+F,eAAe,EAAE;MAC1C,IAAI,CAACiC,YAAY,CAAC,IAAI,CAACjC,eAAe,EAAEgC,UAAU,CAAC;KACpD,MAAM;MACL,IAAI,CAACE,YAAY,CAACF,UAAU,CAAC;;EAEjC;EAEAE,YAAYA,CAACF,UAAe;IAC1B,IAAI,CAAClG,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC2D,aAAa,CAACyC,YAAY,CAACF,UAAU,CAAC,CAAClB,SAAS,CAAC;MACpDC,IAAI,EAAGK,MAAM,IAAI;QACf,IAAI,CAACvB,OAAO,CAACsC,IAAI,CAACf,MAAM,CAAC;QACzB,IAAI,CAACJ,YAAY,EAAE;QACnB,IAAI,CAACoB,WAAW,CAAC,yBAAyB,CAAC;QAC3C,IAAI,CAAClH,UAAU,EAAE;QACjB,IAAI,CAACY,OAAO,GAAG,KAAK;MACtB,CAAC;MACDmF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,sCAAsC,CAAC;QACtD,IAAI,CAACpF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmG,YAAYA,CAACJ,EAAU,EAAEG,UAAe;IACtC,IAAI,CAAClG,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC2D,aAAa,CAACwC,YAAY,CAACJ,EAAE,EAAEG,UAAU,CAAC,CAAClB,SAAS,CAAC;MACxDC,IAAI,EAAGsB,aAAa,IAAI;QACtB,MAAMC,KAAK,GAAG,IAAI,CAACzC,OAAO,CAAC0C,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKA,EAAE,CAAC;QACtD,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACzC,OAAO,CAACyC,KAAK,CAAC,GAAGD,aAAa;UACnC,IAAI,CAACrB,YAAY,EAAE;;QAErB,IAAI,CAACoB,WAAW,CAAC,4BAA4B,CAAC;QAC9C,IAAI,CAAClH,UAAU,EAAE;QACjB,IAAI,CAACY,OAAO,GAAG,KAAK;MACtB,CAAC;MACDmF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,SAAS,CAAC,0CAA0C,CAAC;QAC1D,IAAI,CAACpF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAkC,YAAYA,CAACoD,MAAc;IACzB,IAAIqB,OAAO,CAAC,iDAAiDrB,MAAM,CAAC5F,GAAG,KAAK,CAAC,EAAE;MAC7E,IAAI,CAACM,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC2D,aAAa,CAACzB,YAAY,CAACoD,MAAM,CAACS,EAAE,CAAC,CAACf,SAAS,CAAC;QACnDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAClB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACsB,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKT,MAAM,CAACS,EAAE,CAAC;UAC3D,IAAI,CAACb,YAAY,EAAE;UACnB,IAAI,CAACoB,WAAW,CAAC,6BAA6B,CAAC;UAC/C,IAAI,CAACtG,OAAO,GAAG,KAAK;QACtB,CAAC;QACDmF,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACC,SAAS,CAAC,yCAAyC,CAAC;UACzD,IAAI,CAACpF,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEAqB,cAAcA,CAACtB,MAAoB;IACjC,MAAM6G,MAAM,GAAG,IAAI,CAAC9G,aAAa,CAAC+G,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAClJ,KAAK,KAAKmC,MAAM,CAAC;IACnE,OAAO6G,MAAM,GAAGA,MAAM,CAAC7I,KAAK,GAAG,SAAS;EAC1C;EAEAoD,cAAcA,CAACpB,MAAoB;IACjC,QAAQA,MAAM;MACZ,KAAKlD,YAAY,CAACuH,KAAK;QACrB,OAAO,cAAc;MACvB,KAAKvH,YAAY,CAACwH,OAAO;QACvB,OAAO,gBAAgB;MACzB,KAAKxH,YAAY,CAACyH,QAAQ;QACxB,OAAO,iBAAiB;MAC1B;QACE,OAAO,EAAE;;EAEf;EAEQ2B,oBAAoBA,CAAA;IAC1Bc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvH,UAAU,CAACwH,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC3H,UAAU,CAAC4H,GAAG,CAACF,GAAG,CAAC;MACxCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQhB,WAAWA,CAACiB,OAAe;IACjC,IAAI,CAACzD,QAAQ,CAACU,IAAI,CAAC+C,OAAO,EAAE,QAAQ,EAAE;MACpC9C,QAAQ,EAAE,IAAI;MACd+C,UAAU,EAAE,CAAC,kBAAkB;KAChC,CAAC;EACJ;EAEQpC,SAASA,CAACmC,OAAe;IAC/B,IAAI,CAACzD,QAAQ,CAACU,IAAI,CAAC+C,OAAO,EAAE,QAAQ,EAAE;MACpC9C,QAAQ,EAAE,IAAI;MACd+C,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEA;EACA,IAAI9H,GAAGA,CAAA;IAAK,OAAO,IAAI,CAACD,UAAU,CAAC4H,GAAG,CAAC,KAAK,CAAC;EAAE;EAC/C,IAAIzH,KAAKA,CAAA;IAAK,OAAO,IAAI,CAACH,UAAU,CAAC4H,GAAG,CAAC,OAAO,CAAC;EAAE;EACnD,IAAIxG,SAASA,CAAA;IAAK,OAAO,IAAI,CAACpB,UAAU,CAAC4H,GAAG,CAAC,WAAW,CAAC;EAAE;EAC3D,IAAIxH,OAAOA,CAAA;IAAK,OAAO,IAAI,CAACJ,UAAU,CAAC4H,GAAG,CAAC,SAAS,CAAC;EAAE;EACvD,IAAItH,MAAMA,CAAA;IAAK,OAAO,IAAI,CAACN,UAAU,CAAC4H,GAAG,CAAC,QAAQ,CAAC;EAAE;;;uBAtO1C5D,gBAAgB,EAAA3G,EAAA,CAAA2K,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7K,EAAA,CAAA2K,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/K,EAAA,CAAA2K,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjL,EAAA,CAAA2K,iBAAA,CAAAO,EAAA,CAAAC,SAAA,GAAAnL,EAAA,CAAA2K,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB1E,gBAAgB;MAAA2E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7B5L,EAAA,CAAAC,cAAA,aAA+B;UAIfD,EAAA,CAAAU,MAAA,aAAM;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAC3BX,EAAA,CAAAU,MAAA,4BACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAA4B,UAAA,IAAAkK,kCAAA,oBAIS;UACX9L,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAC,cAAA,kBAA+B;UAGdD,EAAA,CAAAU,MAAA,kBAAU;UAAAV,EAAA,CAAAW,YAAA,EAAY;UACjCX,EAAA,CAAAC,cAAA,gBAC4C;UAD5BD,EAAA,CAAAE,UAAA,2BAAA6L,0DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA3E,UAAA,GAAA8E,MAAA;UAAA,EAAwB,mBAAAC,kDAAA;YAAA,OAAUJ,GAAA,CAAAhD,cAAA,EAAgB;UAAA,EAA1B;UAAxC7I,EAAA,CAAAW,YAAA,EAC4C;UAC5CX,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAU,MAAA,cAAM;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAGvCX,EAAA,CAAAC,cAAA,yBAA0D;UAC7CD,EAAA,CAAAU,MAAA,0BAAkB;UAAAV,EAAA,CAAAW,YAAA,EAAY;UACzCX,EAAA,CAAAC,cAAA,qBAAkF;UAAtED,EAAA,CAAAE,UAAA,yBAAAgM,6DAAAF,MAAA;YAAA,OAAAH,GAAA,CAAA1E,cAAA,GAAA6E,MAAA;UAAA,EAA0B,6BAAAG,iEAAA;YAAA,OAAoBN,GAAA,CAAA/C,oBAAA,EAAsB;UAAA,EAA1C;UACpC9I,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAU,MAAA,wBAAgB;UAAAV,EAAA,CAAAW,YAAA,EAAa;UACxDX,EAAA,CAAA4B,UAAA,KAAAwK,uCAAA,yBAEa;UACfpM,EAAA,CAAAW,YAAA,EAAa;UAGfX,EAAA,CAAAC,cAAA,kBAAwE;UAAhDD,EAAA,CAAAE,UAAA,mBAAAmM,mDAAA;YAAA,OAASR,GAAA,CAAAhE,WAAA,EAAa;UAAA,EAAC;UAC7C7H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAU,MAAA,eAAO;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAMlCX,EAAA,CAAA4B,UAAA,KAAA0K,qCAAA,yBAqEW;UAGXtM,EAAA,CAAAC,cAAA,oBAA6B;UAGvBD,EAAA,CAAAU,MAAA,IACF;UAAAV,EAAA,CAAAW,YAAA,EAAiB;UAGnBX,EAAA,CAAAC,cAAA,wBAAkB;UAChBD,EAAA,CAAA4B,UAAA,KAAA2K,gCAAA,kBAGM;UAENvM,EAAA,CAAA4B,UAAA,KAAA4K,gCAAA,kBAQM;UAENxM,EAAA,CAAA4B,UAAA,KAAA6K,kCAAA,qBAoEQ;UACVzM,EAAA,CAAAW,YAAA,EAAmB;;;UArMVX,EAAA,CAAAe,SAAA,GAAoC;UAApCf,EAAA,CAAAY,UAAA,SAAAiL,GAAA,CAAArI,WAAA,CAAAC,gBAAA,GAAoC;UAWzBzD,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAAY,UAAA,YAAAiL,GAAA,CAAA3E,UAAA,CAAwB;UAO5BlH,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAY,UAAA,UAAAiL,GAAA,CAAA1E,cAAA,CAA0B;UACxBnH,EAAA,CAAAe,SAAA,GAAc;UAAdf,EAAA,CAAAY,UAAA,eAAc;UACKZ,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAAY,UAAA,YAAAiL,GAAA,CAAA7I,aAAA,CAAgB;UAa5ChD,EAAA,CAAAe,SAAA,GAAc;UAAdf,EAAA,CAAAY,UAAA,SAAAiL,GAAA,CAAAxE,QAAA,CAAc;UA2EnBrH,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAgB,kBAAA,yBAAA6K,GAAA,CAAApF,eAAA,CAAAiG,MAAA,OACF;UAIM1M,EAAA,CAAAe,SAAA,GAAa;UAAbf,EAAA,CAAAY,UAAA,SAAAiL,GAAA,CAAA3I,OAAA,CAAa;UAKblD,EAAA,CAAAe,SAAA,GAA8C;UAA9Cf,EAAA,CAAAY,UAAA,UAAAiL,GAAA,CAAA3I,OAAA,IAAA2I,GAAA,CAAApF,eAAA,CAAAiG,MAAA,OAA8C;UAUH1M,EAAA,CAAAe,SAAA,GAA4C;UAA5Cf,EAAA,CAAAY,UAAA,UAAAiL,GAAA,CAAA3I,OAAA,IAAA2I,GAAA,CAAApF,eAAA,CAAAiG,MAAA,KAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}