{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DocumentService = /*#__PURE__*/(() => {\n  class DocumentService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = '/api/documents';\n      this.TYPE_DOCUMENT_URL = '/api/type-documents';\n    }\n    // Documents\n    getDocuments() {\n      return this.http.get(this.API_URL);\n    }\n    getDocument(id) {\n      return this.http.get(`${this.API_URL}/${id}`);\n    }\n    createDocument(document) {\n      return this.http.post(this.API_URL, document);\n    }\n    updateDocument(id, document) {\n      return this.http.put(`${this.API_URL}/${id}`, document);\n    }\n    deleteDocument(id) {\n      return this.http.delete(`${this.API_URL}/${id}`);\n    }\n    approveDocument(id) {\n      return this.http.patch(`${this.API_URL}/${id}/approve`, {});\n    }\n    rejectDocument(id, commentaire) {\n      return this.http.patch(`${this.API_URL}/${id}/reject`, {\n        commentaire\n      });\n    }\n    getDocumentsByStatut(statut) {\n      const params = new HttpParams().set('statut', statut.toString());\n      return this.http.get(this.API_URL, {\n        params\n      });\n    }\n    getDocumentsByType(typeId) {\n      const params = new HttpParams().set('typeId', typeId);\n      return this.http.get(this.API_URL, {\n        params\n      });\n    }\n    generatePdf(id) {\n      return this.http.get(`${this.API_URL}/${id}/pdf`, {\n        responseType: 'blob'\n      });\n    }\n    // Méthodes spécifiques pour les utilisateurs\n    getMyDocuments() {\n      return this.http.get(`${this.API_URL}/my-documents`);\n    }\n    updateDocumentStatus(id, statut) {\n      return this.http.patch(`${this.API_URL}/${id}/status`, {\n        statut\n      });\n    }\n    // Types de documents\n    getTypesDocument() {\n      return this.http.get(this.TYPE_DOCUMENT_URL);\n    }\n    getTypeDocument(id) {\n      return this.http.get(`${this.TYPE_DOCUMENT_URL}/${id}`);\n    }\n    createTypeDocument(typeDocument) {\n      return this.http.post(this.TYPE_DOCUMENT_URL, typeDocument);\n    }\n    updateTypeDocument(id, typeDocument) {\n      return this.http.put(`${this.TYPE_DOCUMENT_URL}/${id}`, typeDocument);\n    }\n    deleteTypeDocument(id) {\n      return this.http.delete(`${this.TYPE_DOCUMENT_URL}/${id}`);\n    }\n    static {\n      this.ɵfac = function DocumentService_Factory(t) {\n        return new (t || DocumentService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DocumentService,\n        factory: DocumentService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DocumentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}