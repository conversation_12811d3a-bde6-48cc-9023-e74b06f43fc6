{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { StatutFournisseur } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/fournisseur.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nexport let FournisseursComponent = /*#__PURE__*/(() => {\n  class FournisseursComponent {\n    constructor(fournisseurService, authService, formBuilder, snackBar) {\n      this.fournisseurService = fournisseurService;\n      this.authService = authService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.fournisseurs = [];\n      this.filteredFournisseurs = [];\n      this.loading = false;\n      this.searchTerm = '';\n      this.selectedStatut = null;\n      this.isEditing = false;\n      this.editingFournisseurId = null;\n      this.showForm = false;\n      // Énumérations pour le template\n      this.StatutFournisseur = StatutFournisseur;\n      this.statutOptions = [{\n        value: StatutFournisseur.Actif,\n        label: 'Actif'\n      }, {\n        value: StatutFournisseur.Inactif,\n        label: 'Inactif'\n      }, {\n        value: StatutFournisseur.Suspendu,\n        label: 'Suspendu'\n      }];\n      // Colonnes à afficher dans le tableau\n      this.displayedColumns = ['nom', 'email', 'telephone', 'statut', 'dateCreation', 'actions'];\n    }\n    ngOnInit() {\n      // Vérifier les permissions\n      if (!this.authService.canManageSuppliers()) {\n        this.snackBar.open('Accès non autorisé', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      this.initializeForm();\n      this.loadFournisseurs();\n    }\n    initializeForm() {\n      this.fournisseurForm = this.formBuilder.group({\n        nom: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        telephone: [''],\n        adresse: ['', [Validators.required]],\n        statut: [StatutFournisseur.Actif, [Validators.required]]\n      });\n    }\n    loadFournisseurs() {\n      this.loading = true;\n      this.fournisseurService.getFournisseurs().subscribe({\n        next: fournisseurs => {\n          this.fournisseurs = fournisseurs;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors du chargement des fournisseurs');\n          this.loading = false;\n        }\n      });\n    }\n    applyFilters() {\n      this.filteredFournisseurs = this.fournisseurs.filter(fournisseur => {\n        const matchesSearch = !this.searchTerm || fournisseur.nom.toLowerCase().includes(this.searchTerm.toLowerCase()) || fournisseur.email.toLowerCase().includes(this.searchTerm.toLowerCase());\n        const matchesStatut = this.selectedStatut === null || fournisseur.statut === this.selectedStatut;\n        return matchesSearch && matchesStatut;\n      });\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    onStatutFilterChange() {\n      this.applyFilters();\n    }\n    showAddForm() {\n      this.isEditing = false;\n      this.editingFournisseurId = null;\n      this.fournisseurForm.reset();\n      this.fournisseurForm.patchValue({\n        statut: StatutFournisseur.Actif\n      });\n      this.showForm = true;\n    }\n    editFournisseur(fournisseur) {\n      this.isEditing = true;\n      this.editingFournisseurId = fournisseur.id;\n      this.fournisseurForm.patchValue({\n        nom: fournisseur.nom,\n        email: fournisseur.email,\n        telephone: fournisseur.telephone,\n        adresse: fournisseur.adresse,\n        statut: fournisseur.statut\n      });\n      this.showForm = true;\n    }\n    cancelForm() {\n      this.showForm = false;\n      this.isEditing = false;\n      this.editingFournisseurId = null;\n      this.fournisseurForm.reset();\n    }\n    onSubmit() {\n      if (this.fournisseurForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      const fournisseurData = this.fournisseurForm.value;\n      if (this.isEditing && this.editingFournisseurId) {\n        this.updateFournisseur(this.editingFournisseurId, fournisseurData);\n      } else {\n        this.createFournisseur(fournisseurData);\n      }\n    }\n    createFournisseur(fournisseurData) {\n      this.loading = true;\n      this.fournisseurService.createFournisseur(fournisseurData).subscribe({\n        next: fournisseur => {\n          this.fournisseurs.push(fournisseur);\n          this.applyFilters();\n          this.showSuccess('Fournisseur créé avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la création du fournisseur');\n          this.loading = false;\n        }\n      });\n    }\n    updateFournisseur(id, fournisseurData) {\n      this.loading = true;\n      this.fournisseurService.updateFournisseur(id, fournisseurData).subscribe({\n        next: updatedFournisseur => {\n          const index = this.fournisseurs.findIndex(f => f.id === id);\n          if (index !== -1) {\n            this.fournisseurs[index] = updatedFournisseur;\n            this.applyFilters();\n          }\n          this.showSuccess('Fournisseur modifié avec succès');\n          this.cancelForm();\n          this.loading = false;\n        },\n        error: error => {\n          this.showError('Erreur lors de la modification du fournisseur');\n          this.loading = false;\n        }\n      });\n    }\n    deleteFournisseur(fournisseur) {\n      if (confirm(`Êtes-vous sûr de vouloir supprimer le fournisseur \"${fournisseur.nom}\" ?`)) {\n        this.loading = true;\n        this.fournisseurService.deleteFournisseur(fournisseur.id).subscribe({\n          next: () => {\n            this.fournisseurs = this.fournisseurs.filter(f => f.id !== fournisseur.id);\n            this.applyFilters();\n            this.showSuccess('Fournisseur supprimé avec succès');\n            this.loading = false;\n          },\n          error: error => {\n            this.showError('Erreur lors de la suppression du fournisseur');\n            this.loading = false;\n          }\n        });\n      }\n    }\n    getStatutLabel(statut) {\n      const option = this.statutOptions.find(opt => opt.value === statut);\n      return option ? option.label : 'Inconnu';\n    }\n    getStatutClass(statut) {\n      switch (statut) {\n        case StatutFournisseur.Actif:\n          return 'statut-actif';\n        case StatutFournisseur.Inactif:\n          return 'statut-inactif';\n        case StatutFournisseur.Suspendu:\n          return 'statut-suspendu';\n        default:\n          return '';\n      }\n    }\n    markFormGroupTouched() {\n      Object.keys(this.fournisseurForm.controls).forEach(key => {\n        const control = this.fournisseurForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    showSuccess(message) {\n      this.snackBar.open(message, 'Fermer', {\n        duration: 3000,\n        panelClass: ['success-snackbar']\n      });\n    }\n    showError(message) {\n      this.snackBar.open(message, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    // Getters pour faciliter l'accès aux contrôles dans le template\n    get nom() {\n      return this.fournisseurForm.get('nom');\n    }\n    get email() {\n      return this.fournisseurForm.get('email');\n    }\n    get telephone() {\n      return this.fournisseurForm.get('telephone');\n    }\n    get adresse() {\n      return this.fournisseurForm.get('adresse');\n    }\n    get statut() {\n      return this.fournisseurForm.get('statut');\n    }\n    static {\n      this.ɵfac = function FournisseursComponent_Factory(t) {\n        return new (t || FournisseursComponent)(i0.ɵɵdirectiveInject(i1.FournisseurService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FournisseursComponent,\n        selectors: [[\"app-fournisseurs\"]],\n        decls: 2,\n        vars: 0,\n        template: function FournisseursComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"fournisseurs works!\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return FournisseursComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}