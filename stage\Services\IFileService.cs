namespace stage.Services
{
    public interface IFileService
    {
        Task<string> SaveFileAsync(IFormFile file, string subfolder = "uploads");
        Task<byte[]> GetFileAsync(string filePath);
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> FileExistsAsync(string filePath);
        string GetContentType(string filePath);
        Task<string> SaveBase64FileAsync(string base64Content, string fileName, string subfolder = "uploads");
        Task<List<string>> GetAllowedExtensions();
        Task<bool> IsValidFileType(string fileName);
        Task<long> GetFileSizeAsync(string filePath);
    }
}
