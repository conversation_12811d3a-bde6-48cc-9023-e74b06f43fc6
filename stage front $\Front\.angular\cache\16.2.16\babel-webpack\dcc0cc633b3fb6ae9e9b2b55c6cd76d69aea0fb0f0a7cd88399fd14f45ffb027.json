{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FournisseursComponent {\n  static {\n    this.ɵfac = function FournisseursComponent_Factory(t) {\n      return new (t || FournisseursComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FournisseursComponent,\n      selectors: [[\"app-fournisseurs\"]],\n      decls: 2,\n      vars: 0,\n      template: function FournisseursComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"fournisseurs works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FournisseursComponent", "selectors", "decls", "vars", "template", "FournisseursComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\fournisseurs\\fournisseurs.component.ts", "C:\\Users\\<USER>\\source\\repos\\stage\\stage front $\\Front\\src\\app\\fournisseurs\\fournisseurs.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-fournisseurs',\n  templateUrl: './fournisseurs.component.html',\n  styleUrls: ['./fournisseurs.component.css']\n})\nexport class FournisseursComponent {\n\n}\n", "<p>fournis<PERSON><PERSON> works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}